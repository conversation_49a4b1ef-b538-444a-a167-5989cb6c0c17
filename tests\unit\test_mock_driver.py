"""Unit tests for the mock driver."""

import pytest
import asyncio
from datetime import datetime

from bts.infrastructure.drivers.mock import MockDriver
from bts.infrastructure.drivers.base import ConnectionType, DriverCapabilities


class TestMockDriver:
    """Test cases for MockDriver."""
    
    @pytest.mark.asyncio
    async def test_driver_creation(self, mock_instrument):
        """Test mock driver creation."""
        driver = MockDriver(mock_instrument)
        
        assert driver.instrument == mock_instrument
        assert not driver.is_connected
        assert driver.connection_type == ConnectionType.MOCK
        assert isinstance(driver.capabilities, DriverCapabilities)
    
    @pytest.mark.asyncio
    async def test_driver_capabilities(self, mock_instrument):
        """Test mock driver capabilities."""
        driver = MockDriver(mock_instrument)
        caps = driver.capabilities
        
        assert caps.supports_voltage_control
        assert caps.supports_current_control
        assert caps.supports_power_control
        assert caps.can_measure_voltage
        assert caps.can_measure_current
        assert caps.can_measure_temperature
        assert caps.supports_eis
        assert caps.supports_pulse
        assert caps.max_channels == 8
        assert caps.independent_channels
    
    @pytest.mark.asyncio
    async def test_connection_lifecycle(self, mock_instrument):
        """Test connection and disconnection."""
        driver = MockDriver(mock_instrument)
        
        # Initial state
        assert not driver.is_connected
        
        # Connect
        success = await driver.connect()
        assert success
        assert driver.is_connected
        
        # Disconnect
        success = await driver.disconnect()
        assert success
        assert not driver.is_connected
    
    @pytest.mark.asyncio
    async def test_identification(self, mock_driver):
        """Test instrument identification."""
        ident = await mock_driver.identify()
        
        assert "manufacturer" in ident
        assert "model" in ident
        assert "serial_number" in ident
        assert "firmware_version" in ident
        assert ident["manufacturer"] == "Mock Instruments Inc."
        assert ident["model"] == "BTS-MOCK-8000"
    
    @pytest.mark.asyncio
    async def test_reset(self, mock_driver):
        """Test instrument reset."""
        # Enable some channels first
        await mock_driver.enable_channel(1)
        await mock_driver.enable_channel(2)
        
        # Reset
        success = await mock_driver.reset()
        assert success
        
        # Verify channels are disabled
        # Note: In a real implementation, we'd check channel states
    
    @pytest.mark.asyncio
    async def test_status(self, mock_driver):
        """Test getting instrument status."""
        status = await mock_driver.get_status()
        
        assert "connected" in status
        assert "active_channels" in status
        assert "total_channels" in status
        assert "temperature" in status
        assert status["connected"] == True
        assert status["total_channels"] == 8
    
    @pytest.mark.asyncio
    async def test_channel_management(self, mock_driver):
        """Test channel enable/disable operations."""
        channel = 1
        
        # Enable channel
        success = await mock_driver.enable_channel(channel)
        assert success
        
        # Disable channel
        success = await mock_driver.disable_channel(channel)
        assert success
        
        # Test invalid channel
        success = await mock_driver.enable_channel(99)
        assert not success
    
    @pytest.mark.asyncio
    async def test_voltage_control(self, mock_driver):
        """Test voltage control operations."""
        channel = 1
        voltage = 3.7
        
        # Enable channel first
        await mock_driver.enable_channel(channel)
        
        # Set voltage
        success = await mock_driver.set_voltage(channel, voltage)
        assert success
        
        # Test invalid voltage
        success = await mock_driver.set_voltage(channel, 10.0)  # Outside range
        assert not success
        
        # Test invalid channel
        success = await mock_driver.set_voltage(99, voltage)
        assert not success
    
    @pytest.mark.asyncio
    async def test_current_control(self, mock_driver):
        """Test current control operations."""
        channel = 1
        current = 1.0
        
        # Enable channel first
        await mock_driver.enable_channel(channel)
        
        # Set current
        success = await mock_driver.set_current(channel, current)
        assert success
        
        # Test invalid current
        success = await mock_driver.set_current(channel, 200.0)  # Outside range
        assert not success
        
        # Test invalid channel
        success = await mock_driver.set_current(99, current)
        assert not success
    
    @pytest.mark.asyncio
    async def test_power_control(self, mock_driver):
        """Test power control operations."""
        channel = 1
        power = 10.0
        
        # Enable channel first
        await mock_driver.enable_channel(channel)
        
        # Set power
        success = await mock_driver.set_power(channel, power)
        assert success
        
        # Test invalid channel
        success = await mock_driver.set_power(99, power)
        assert not success
    
    @pytest.mark.asyncio
    async def test_measurement_reading(self, mock_driver):
        """Test reading measurements."""
        channel = 1
        
        # Enable channel first
        await mock_driver.enable_channel(channel)
        
        # Set some values to get realistic measurements
        await mock_driver.set_voltage(channel, 3.7)
        
        # Allow simulation to update
        await asyncio.sleep(0.2)
        
        # Read measurement
        measurement = await mock_driver.read_measurement(channel)
        assert measurement is not None
        assert measurement.channel_id == str(channel)
        assert measurement.voltage is not None
        assert measurement.current is not None
        assert measurement.temperature is not None
        assert isinstance(measurement.timestamp, datetime)
        
        # Test invalid channel
        measurement = await mock_driver.read_measurement(99)
        assert measurement is None
    
    @pytest.mark.asyncio
    async def test_read_all_measurements(self, mock_driver):
        """Test reading measurements from all channels."""
        # Enable multiple channels
        await mock_driver.enable_channel(1)
        await mock_driver.enable_channel(2)
        await mock_driver.enable_channel(3)
        
        # Set different values on each channel
        await mock_driver.set_voltage(1, 3.5)
        await mock_driver.set_voltage(2, 3.7)
        await mock_driver.set_voltage(3, 3.9)
        
        # Allow simulation to update
        await asyncio.sleep(0.2)
        
        # Read all measurements
        measurements = await mock_driver.read_all_measurements()
        
        assert len(measurements) == 3
        assert 1 in measurements
        assert 2 in measurements
        assert 3 in measurements
        
        for channel_num, measurement in measurements.items():
            assert measurement.channel_id == str(channel_num)
            assert measurement.voltage is not None
    
    @pytest.mark.asyncio
    async def test_channel_reservation(self, mock_driver):
        """Test channel reservation system."""
        channel = 1
        
        # Reserve channel
        success = mock_driver.reserve_channel(channel)
        assert success
        assert channel not in mock_driver.available_channels
        
        # Try to reserve again
        success = mock_driver.reserve_channel(channel)
        assert not success
        
        # Release channel
        success = mock_driver.release_channel(channel)
        assert success
        assert channel in mock_driver.available_channels
        
        # Test invalid channel
        success = mock_driver.reserve_channel(99)
        assert not success
    
    @pytest.mark.asyncio
    async def test_simulation_behavior(self, mock_driver):
        """Test simulation behavior over time."""
        channel = 1
        
        # Enable channel and set constant current
        await mock_driver.enable_channel(channel)
        await mock_driver.set_current(channel, 1.0)
        
        # Take initial measurement
        await asyncio.sleep(0.1)
        measurement1 = await mock_driver.read_measurement(channel)
        
        # Wait and take another measurement
        await asyncio.sleep(0.2)
        measurement2 = await mock_driver.read_measurement(channel)
        
        # Verify measurements are different (simulation is running)
        assert measurement1.timestamp != measurement2.timestamp
        
        # In CC mode, current should be close to target
        assert abs(measurement2.current - 1.0) < 0.1
        
        # Voltage should respond to current (simple battery model)
        assert measurement2.voltage is not None
        assert 2.0 < measurement2.voltage < 5.0  # Reasonable range
    
    @pytest.mark.asyncio
    async def test_context_manager(self, mock_instrument):
        """Test using driver as async context manager."""
        async with MockDriver(mock_instrument) as driver:
            assert driver.is_connected
            
            # Use driver
            await driver.enable_channel(1)
            measurement = await driver.read_measurement(1)
            # measurement might be None if channel not enabled properly
        
        # Driver should be disconnected after context
        assert not driver.is_connected
    
    @pytest.mark.asyncio
    async def test_error_conditions(self, mock_driver):
        """Test error handling in mock driver."""
        # Test operations on disabled channel
        measurement = await mock_driver.read_measurement(1)
        assert measurement is None  # Channel not enabled
        
        # Test setting voltage on disabled channel
        success = await mock_driver.set_voltage(1, 3.7)
        assert not success
        
        # Test operations after disconnect
        await mock_driver.disconnect()
        success = await mock_driver.enable_channel(1)
        assert not success  # Should fail when disconnected
    
    @pytest.mark.asyncio
    async def test_last_measurement_storage(self, mock_driver):
        """Test last measurement storage and retrieval."""
        channel = 1
        
        # Initially no measurement
        last_measurement = mock_driver.get_last_measurement(channel)
        assert last_measurement is None
        
        # Enable channel and read measurement
        await mock_driver.enable_channel(channel)
        await asyncio.sleep(0.1)
        measurement = await mock_driver.read_measurement(channel)
        
        # Check stored measurement
        last_measurement = mock_driver.get_last_measurement(channel)
        assert last_measurement is not None
        assert last_measurement.id == measurement.id
        assert last_measurement.channel_id == measurement.channel_id
