"""Instruments management widget."""

import logging

try:
    from PySide6.QtWidgets import (
        QWidget, QVBoxLayout, QHBoxLayout, QLabel, 
        QPushButton, QTableWidget, QTableWidgetItem,
        QGroupBox, QMessageBox
    )
    from PySide6.QtCore import Qt, QTimer
    PYSIDE6_AVAILABLE = True
except ImportError:
    PYSIDE6_AVAILABLE = False

if PYSIDE6_AVAILABLE:
    class InstrumentsWidget(QWidget):
        """Widget for managing instruments."""
        
        def __init__(self, hardware_manager=None, parent=None):
            super().__init__(parent)
            
            self.logger = logging.getLogger(__name__)
            self.hardware_manager = hardware_manager
            
            self.instruments_table = None
            self.setup_ui()
            
            # Update timer
            self.update_timer = QTimer()
            self.update_timer.timeout.connect(self.update_instruments)
            self.update_timer.start(5000)  # Update every 5 seconds
        
        def setup_ui(self):
            """Setup the instruments UI."""
            layout = QVBoxLayout(self)
            
            # Title
            title = QLabel("Instrument Management")
            title.setStyleSheet("font-size: 16px; font-weight: bold;")
            layout.addWidget(title)
            
            # Control buttons
            buttons_layout = QHBoxLayout()
            
            discover_btn = QPushButton("🔍 Discover Instruments")
            discover_btn.clicked.connect(self.discover_instruments)
            buttons_layout.addWidget(discover_btn)
            
            add_btn = QPushButton("➕ Add Instrument")
            add_btn.clicked.connect(self.add_instrument)
            buttons_layout.addWidget(add_btn)
            
            buttons_layout.addStretch()
            layout.addLayout(buttons_layout)
            
            # Instruments table
            self.create_instruments_table(layout)
        
        def create_instruments_table(self, parent_layout):
            """Create the instruments table."""
            group = QGroupBox("Connected Instruments")
            layout = QVBoxLayout(group)
            
            self.instruments_table = QTableWidget()
            self.instruments_table.setColumnCount(6)
            self.instruments_table.setHorizontalHeaderLabels([
                "Name", "Type", "Status", "Channels", "Driver", "Actions"
            ])
            
            layout.addWidget(self.instruments_table)
            parent_layout.addWidget(group)
            
            self.update_instruments()
        
        def update_instruments(self):
            """Update the instruments table."""
            if not self.hardware_manager:
                return
            
            try:
                instruments = self.hardware_manager.get_all_instruments()
                self.instruments_table.setRowCount(len(instruments))
                
                for row, instrument in enumerate(instruments):
                    # Name
                    self.instruments_table.setItem(row, 0, QTableWidgetItem(instrument.name))
                    
                    # Type
                    self.instruments_table.setItem(row, 1, QTableWidgetItem(instrument.instrument_type.value))
                    
                    # Status
                    connected = instrument.id in [i.id for i in self.hardware_manager.get_connected_instruments()]
                    status = "🟢 Connected" if connected else "🔴 Disconnected"
                    self.instruments_table.setItem(row, 2, QTableWidgetItem(status))
                    
                    # Channels
                    self.instruments_table.setItem(row, 3, QTableWidgetItem(str(instrument.max_channels)))
                    
                    # Driver
                    self.instruments_table.setItem(row, 4, QTableWidgetItem(instrument.driver_name or "Unknown"))
                    
                    # Actions - placeholder
                    self.instruments_table.setItem(row, 5, QTableWidgetItem("Connect/Disconnect"))
                
            except Exception as e:
                self.logger.error(f"Error updating instruments table: {e}")
        
        def discover_instruments(self):
            """Discover available instruments."""
            QMessageBox.information(self, "Discover", "Instrument discovery not yet implemented")
        
        def add_instrument(self):
            """Add a new instrument."""
            QMessageBox.information(self, "Add Instrument", "Add instrument dialog not yet implemented")

else:
    class InstrumentsWidget:
        def __init__(self, *args, **kwargs):
            raise ImportError("PySide6 is required for GUI functionality")
