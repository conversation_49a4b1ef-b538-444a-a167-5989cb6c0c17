"""
Battery Testing System (BTS)

A modular, extensible battery testing software for laboratories,
EV pack manufacturers, and cell validation lines.
"""

__version__ = "0.1.0"
__author__ = "Battery Testing System Team"
__email__ = "<EMAIL>"
__license__ = "MIT"

# Core imports for public API
from bts.domain.entities.channel import Channel
from bts.domain.entities.measurement import Measurement
from bts.domain.entities.test import Test
from bts.domain.entities.test_profile import TestProfile
from bts.domain.value_objects.safety_limits import SafetyLimits

# Application services
from bts.application.services.test_engine import TestEngine
from bts.application.services.data_manager import DataManager
from bts.application.services.safety_manager import SafetyManager

# Infrastructure
from bts.infrastructure.config.settings import Settings
from bts.infrastructure.database.connection import DatabaseConnection

__all__ = [
    # Version info
    "__version__",
    "__author__",
    "__email__",
    "__license__",
    
    # Domain entities
    "Channel",
    "Measurement", 
    "Test",
    "TestProfile",
    "SafetyLimits",
    
    # Application services
    "TestEngine",
    "DataManager",
    "SafetyManager",
    
    # Infrastructure
    "Settings",
    "DatabaseConnection",
]
