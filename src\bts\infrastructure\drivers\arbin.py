"""Arbin battery cycler driver."""

import asyncio
import struct
from datetime import datetime
from typing import Dict, Any, Optional
from uuid import uuid4

try:
    import socket
    SOCKET_AVAILABLE = True
except ImportError:
    SOCKET_AVAILABLE = False

from ...domain.entities.measurement import Measurement
from .base import InstrumentDriver, DriverCapabilities, ConnectionType


class ArbinDriver(InstrumentDriver):
    """
    Driver for Arbin battery cyclers.
    
    This driver communicates with Arbin cyclers using their proprietary
    TCP/IP protocol. It supports most common Arbin models including
    BT2000, LBT, and MSTAT series.
    """
    
    def __init__(self, instrument):
        """Initialize the Arbin driver."""
        super().__init__(instrument)
        
        # Connection parameters
        self._socket: Optional[socket.socket] = None
        self._host = "localhost"
        self._port = 23
        self._timeout = 5.0
        
        # Parse connection string
        if instrument.connection_string:
            self._parse_connection_string(instrument.connection_string)
        
        # Protocol state
        self._sequence_number = 0
        self._channel_info: Dict[int, Dict[str, Any]] = {}
    
    @property
    def capabilities(self) -> DriverCapabilities:
        """Get the capabilities of the Arbin driver."""
        return DriverCapabilities(
            supports_voltage_control=True,
            supports_current_control=True,
            supports_power_control=True,
            supports_resistance_control=True,
            can_measure_voltage=True,
            can_measure_current=True,
            can_measure_temperature=True,
            can_measure_power=True,
            can_measure_resistance=True,
            supports_eis=True,
            supports_pulse=True,
            supports_data_logging=True,
            supports_safety_limits=True,
            max_channels=self.instrument.max_channels,
            independent_channels=True,
            voltage_range=(-1.0, 5.0),
            current_range=(-200.0, 200.0),
            voltage_resolution=0.0001,
            current_resolution=0.0001,
            min_sampling_interval=0.1,
            max_sampling_interval=3600.0,
        )
    
    @property
    def connection_type(self) -> ConnectionType:
        """Get the connection type."""
        return ConnectionType.ETHERNET
    
    def _parse_connection_string(self, connection_string: str) -> None:
        """Parse the connection string to extract host and port."""
        try:
            if ":" in connection_string:
                self._host, port_str = connection_string.split(":", 1)
                self._port = int(port_str)
            else:
                self._host = connection_string
        except ValueError:
            self.logger.warning(f"Invalid connection string: {connection_string}")
    
    async def connect(self) -> bool:
        """Connect to the Arbin cycler."""
        if not SOCKET_AVAILABLE:
            self.logger.error("Socket module not available")
            return False
        
        try:
            self.logger.info(f"Connecting to Arbin at {self._host}:{self._port}")
            
            # Create socket connection
            self._socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            self._socket.settimeout(self._timeout)
            
            # Connect to the cycler
            await asyncio.get_event_loop().run_in_executor(
                None, self._socket.connect, (self._host, self._port)
            )
            
            # Send identification command
            if not await self._send_command("*IDN?"):
                raise Exception("Failed to send identification command")
            
            response = await self._receive_response()
            if not response:
                raise Exception("No response to identification command")
            
            self._connected = True
            self.instrument.state = "connected"
            
            # Initialize channel information
            await self._initialize_channels()
            
            self.logger.info("Connected to Arbin cycler")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to connect to Arbin: {e}")
            if self._socket:
                self._socket.close()
                self._socket = None
            return False
    
    async def disconnect(self) -> bool:
        """Disconnect from the Arbin cycler."""
        try:
            if self._socket:
                self._socket.close()
                self._socket = None
            
            self._connected = False
            self.instrument.state = "disconnected"
            self._channel_info.clear()
            
            self.logger.info("Disconnected from Arbin cycler")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to disconnect from Arbin: {e}")
            return False
    
    async def identify(self) -> Dict[str, str]:
        """Get Arbin cycler identification."""
        try:
            if not await self._send_command("*IDN?"):
                return {}
            
            response = await self._receive_response()
            if not response:
                return {}
            
            # Parse Arbin identification response
            # Format: "ARBIN,BT2000,SN123456,V1.2.3"
            parts = response.strip().split(",")
            if len(parts) >= 4:
                return {
                    "manufacturer": parts[0],
                    "model": parts[1],
                    "serial_number": parts[2],
                    "firmware_version": parts[3],
                }
            
            return {"raw_response": response}
            
        except Exception as e:
            self.logger.error(f"Failed to identify Arbin: {e}")
            return {}
    
    async def reset(self) -> bool:
        """Reset the Arbin cycler."""
        try:
            # Send reset command to all channels
            for channel in range(1, self.capabilities.max_channels + 1):
                await self._send_command(f"CHAN {channel}:STOP")
                await self._send_command(f"CHAN {channel}:CURR 0")
                await self._send_command(f"CHAN {channel}:VOLT 0")
            
            self.logger.info("Arbin cycler reset complete")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to reset Arbin: {e}")
            return False
    
    async def get_status(self) -> Dict[str, Any]:
        """Get Arbin cycler status."""
        try:
            status = {
                "connected": self.is_connected,
                "active_channels": 0,
                "total_channels": self.capabilities.max_channels,
                "errors": [],
                "warnings": [],
            }
            
            # Get status for each channel
            for channel in range(1, self.capabilities.max_channels + 1):
                if await self._send_command(f"CHAN {channel}:STAT?"):
                    response = await self._receive_response()
                    if response and "ACTIVE" in response:
                        status["active_channels"] += 1
            
            return status
            
        except Exception as e:
            self.logger.error(f"Failed to get Arbin status: {e}")
            return {"connected": False, "error": str(e)}
    
    async def get_channel_count(self) -> int:
        """Get the number of available channels."""
        return self.capabilities.max_channels
    
    async def enable_channel(self, channel: int) -> bool:
        """Enable an Arbin channel."""
        if not await self._validate_channel(channel):
            return False
        
        try:
            # Send enable command
            if await self._send_command(f"CHAN {channel}:ENAB ON"):
                self.logger.info(f"Enabled Arbin channel {channel}")
                return True
            return False
            
        except Exception as e:
            self.logger.error(f"Failed to enable Arbin channel {channel}: {e}")
            return False
    
    async def disable_channel(self, channel: int) -> bool:
        """Disable an Arbin channel."""
        if not await self._validate_channel(channel):
            return False
        
        try:
            # Send disable command
            if await self._send_command(f"CHAN {channel}:ENAB OFF"):
                self.logger.info(f"Disabled Arbin channel {channel}")
                return True
            return False
            
        except Exception as e:
            self.logger.error(f"Failed to disable Arbin channel {channel}: {e}")
            return False
    
    async def set_voltage(self, channel: int, voltage: float) -> bool:
        """Set voltage on an Arbin channel."""
        if not await self._validate_channel(channel):
            return False
        
        if not await self._validate_voltage(voltage):
            return False
        
        try:
            # Send voltage command
            if await self._send_command(f"CHAN {channel}:VOLT {voltage:.6f}"):
                self.logger.info(f"Set Arbin channel {channel} voltage to {voltage}V")
                return True
            return False
            
        except Exception as e:
            self.logger.error(f"Failed to set voltage on Arbin channel {channel}: {e}")
            return False
    
    async def set_current(self, channel: int, current: float) -> bool:
        """Set current on an Arbin channel."""
        if not await self._validate_channel(channel):
            return False
        
        if not await self._validate_current(current):
            return False
        
        try:
            # Send current command
            if await self._send_command(f"CHAN {channel}:CURR {current:.6f}"):
                self.logger.info(f"Set Arbin channel {channel} current to {current}A")
                return True
            return False
            
        except Exception as e:
            self.logger.error(f"Failed to set current on Arbin channel {channel}: {e}")
            return False
    
    async def set_power(self, channel: int, power: float) -> bool:
        """Set power on an Arbin channel."""
        if not await self._validate_channel(channel):
            return False
        
        try:
            # Send power command
            if await self._send_command(f"CHAN {channel}:POW {power:.6f}"):
                self.logger.info(f"Set Arbin channel {channel} power to {power}W")
                return True
            return False
            
        except Exception as e:
            self.logger.error(f"Failed to set power on Arbin channel {channel}: {e}")
            return False
    
    async def read_measurement(self, channel: int) -> Optional[Measurement]:
        """Read a measurement from an Arbin channel."""
        if not await self._validate_channel(channel):
            return None
        
        try:
            # Request measurement data
            if not await self._send_command(f"CHAN {channel}:MEAS?"):
                return None
            
            response = await self._receive_response()
            if not response:
                return None
            
            # Parse measurement response
            # Format: "V,I,T,CAP,ENERGY,TIME"
            values = response.strip().split(",")
            if len(values) < 6:
                return None
            
            measurement = Measurement(
                id=uuid4(),
                timestamp=datetime.utcnow(),
                channel_id=str(channel),
                voltage=float(values[0]),
                current=float(values[1]),
                temperature=float(values[2]) if values[2] != "N/A" else None,
                capacity=float(values[3]),
                energy=float(values[4]),
                power=float(values[0]) * float(values[1]),
            )
            
            self._store_measurement(channel, measurement)
            return measurement
            
        except Exception as e:
            self.logger.error(f"Failed to read measurement from Arbin channel {channel}: {e}")
            return None
    
    async def read_all_measurements(self) -> Dict[int, Measurement]:
        """Read measurements from all active Arbin channels."""
        measurements = {}
        
        for channel in range(1, self.capabilities.max_channels + 1):
            measurement = await self.read_measurement(channel)
            if measurement:
                measurements[channel] = measurement
        
        return measurements
    
    async def _initialize_channels(self) -> None:
        """Initialize channel information."""
        for channel in range(1, self.capabilities.max_channels + 1):
            self._channel_info[channel] = {
                "enabled": False,
                "mode": "idle",
                "last_measurement": None,
            }
    
    async def _send_command(self, command: str) -> bool:
        """Send a command to the Arbin cycler."""
        if not self._socket:
            return False
        
        try:
            # Add sequence number and terminator
            full_command = f"{self._sequence_number:04d}:{command}\r\n"
            self._sequence_number = (self._sequence_number + 1) % 10000
            
            # Send command
            await asyncio.get_event_loop().run_in_executor(
                None, self._socket.send, full_command.encode('ascii')
            )
            
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to send command '{command}': {e}")
            return False
    
    async def _receive_response(self) -> Optional[str]:
        """Receive a response from the Arbin cycler."""
        if not self._socket:
            return None
        
        try:
            # Receive response
            data = await asyncio.get_event_loop().run_in_executor(
                None, self._socket.recv, 1024
            )
            
            if not data:
                return None
            
            response = data.decode('ascii').strip()
            return response
            
        except Exception as e:
            self.logger.error(f"Failed to receive response: {e}")
            return None
