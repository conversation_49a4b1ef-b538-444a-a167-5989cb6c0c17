"""Instrument entity for hardware devices."""

from dataclasses import dataclass, field
from datetime import datetime
from enum import Enum
from typing import Dict, Any, Optional, List
from uuid import UUID, uuid4


class InstrumentType(Enum):
    """Types of instruments."""
    CYCLER = "cycler"              # Battery cycler
    SMU = "smu"                    # Source Measure Unit
    DAQ = "daq"                    # Data Acquisition
    POWER_SUPPLY = "power_supply"  # Power Supply
    ELECTRONIC_LOAD = "electronic_load"  # Electronic Load
    MULTIMETER = "multimeter"      # Digital Multimeter
    TEMPERATURE = "temperature"    # Temperature Controller
    PRESSURE = "pressure"          # Pressure Sensor
    CUSTOM = "custom"              # Custom instrument


class InstrumentState(Enum):
    """States that an instrument can be in."""
    DISCONNECTED = "disconnected"
    CONNECTING = "connecting"
    CONNECTED = "connected"
    READY = "ready"
    BUSY = "busy"
    ERROR = "error"
    MAINTENANCE = "maintenance"


@dataclass
class Instrument:
    """
    Represents a hardware instrument for battery testing.
    """
    
    # Core identification
    id: str
    name: str
    instrument_type: InstrumentType
    manufacturer: Optional[str] = None
    model: Optional[str] = None
    serial_number: Optional[str] = None
    
    # Connection information
    connection_string: Optional[str] = None  # e.g., "COM3", "192.168.1.100:502"
    driver_name: Optional[str] = None
    
    # State management
    state: InstrumentState = InstrumentState.DISCONNECTED
    created_at: datetime = field(default_factory=datetime.utcnow)
    updated_at: datetime = field(default_factory=datetime.utcnow)
    last_connected: Optional[datetime] = None
    
    # Capabilities
    max_channels: int = 1
    max_voltage: Optional[float] = None
    max_current: Optional[float] = None
    max_power: Optional[float] = None
    voltage_resolution: Optional[float] = None
    current_resolution: Optional[float] = None
    
    # Configuration
    configuration: Dict[str, Any] = field(default_factory=dict)
    calibration_data: Dict[str, Any] = field(default_factory=dict)
    
    # Status information
    firmware_version: Optional[str] = None
    error_message: Optional[str] = None
    warning_message: Optional[str] = None
    
    # Metadata
    description: Optional[str] = None
    location: Optional[str] = None
    tags: List[str] = field(default_factory=list)
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    def __post_init__(self):
        """Validate instrument data after initialization."""
        if not self.id:
            raise ValueError("Instrument ID cannot be empty")
        
        if not self.name:
            raise ValueError("Instrument name cannot be empty")
        
        if self.max_channels < 1:
            raise ValueError("Max channels must be at least 1")
    
    @property
    def is_connected(self) -> bool:
        """Check if instrument is connected."""
        return self.state in [InstrumentState.CONNECTED, InstrumentState.READY, InstrumentState.BUSY]
    
    @property
    def is_ready(self) -> bool:
        """Check if instrument is ready for use."""
        return self.state == InstrumentState.READY
    
    @property
    def is_busy(self) -> bool:
        """Check if instrument is busy."""
        return self.state == InstrumentState.BUSY
    
    @property
    def has_error(self) -> bool:
        """Check if instrument has an error."""
        return self.state == InstrumentState.ERROR
    
    @property
    def full_name(self) -> str:
        """Get full instrument name including manufacturer and model."""
        parts = [self.name]
        if self.manufacturer:
            parts.append(self.manufacturer)
        if self.model:
            parts.append(self.model)
        return " - ".join(parts)
    
    def connect(self) -> None:
        """Mark instrument as connecting."""
        if self.state == InstrumentState.MAINTENANCE:
            raise ValueError("Cannot connect instrument in maintenance mode")
        
        self.state = InstrumentState.CONNECTING
        self.error_message = None
        self._update_timestamp()
    
    def connected(self) -> None:
        """Mark instrument as connected."""
        if self.state != InstrumentState.CONNECTING:
            raise ValueError("Instrument must be in connecting state")
        
        self.state = InstrumentState.CONNECTED
        self.last_connected = datetime.utcnow()
        self._update_timestamp()
    
    def ready(self) -> None:
        """Mark instrument as ready for use."""
        if not self.is_connected:
            raise ValueError("Instrument must be connected first")
        
        self.state = InstrumentState.READY
        self._update_timestamp()
    
    def busy(self) -> None:
        """Mark instrument as busy."""
        if not self.is_ready:
            raise ValueError("Instrument must be ready first")
        
        self.state = InstrumentState.BUSY
        self._update_timestamp()
    
    def disconnect(self) -> None:
        """Mark instrument as disconnected."""
        self.state = InstrumentState.DISCONNECTED
        self._update_timestamp()
    
    def set_error(self, error_message: str) -> None:
        """Set instrument to error state with message."""
        self.state = InstrumentState.ERROR
        self.error_message = error_message
        self._update_timestamp()
    
    def clear_error(self) -> None:
        """Clear error state."""
        if self.has_error:
            self.state = InstrumentState.DISCONNECTED
            self.error_message = None
            self._update_timestamp()
    
    def set_warning(self, warning_message: str) -> None:
        """Set a warning message."""
        self.warning_message = warning_message
        self._update_timestamp()
    
    def clear_warning(self) -> None:
        """Clear warning message."""
        self.warning_message = None
        self._update_timestamp()
    
    def enter_maintenance(self) -> None:
        """Put instrument in maintenance mode."""
        self.state = InstrumentState.MAINTENANCE
        self._update_timestamp()
    
    def exit_maintenance(self) -> None:
        """Exit maintenance mode."""
        if self.state == InstrumentState.MAINTENANCE:
            self.state = InstrumentState.DISCONNECTED
            self._update_timestamp()
    
    def update_configuration(self, config: Dict[str, Any]) -> None:
        """Update instrument configuration."""
        self.configuration.update(config)
        self._update_timestamp()
    
    def get_configuration(self, key: str, default: Any = None) -> Any:
        """Get a configuration value."""
        return self.configuration.get(key, default)
    
    def update_calibration(self, calibration: Dict[str, Any]) -> None:
        """Update calibration data."""
        self.calibration_data.update(calibration)
        self._update_timestamp()
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert instrument to dictionary for serialization."""
        data = {
            "id": self.id,
            "name": self.name,
            "instrument_type": self.instrument_type.value,
            "manufacturer": self.manufacturer,
            "model": self.model,
            "serial_number": self.serial_number,
            "connection_string": self.connection_string,
            "driver_name": self.driver_name,
            "state": self.state.value,
            "created_at": self.created_at.isoformat(),
            "updated_at": self.updated_at.isoformat(),
            "last_connected": self.last_connected.isoformat() if self.last_connected else None,
            "max_channels": self.max_channels,
            "max_voltage": self.max_voltage,
            "max_current": self.max_current,
            "max_power": self.max_power,
            "voltage_resolution": self.voltage_resolution,
            "current_resolution": self.current_resolution,
            "configuration": self.configuration,
            "calibration_data": self.calibration_data,
            "firmware_version": self.firmware_version,
            "error_message": self.error_message,
            "warning_message": self.warning_message,
            "description": self.description,
            "location": self.location,
            "tags": self.tags,
            "metadata": self.metadata,
        }
        
        return data
    
    def _update_timestamp(self) -> None:
        """Update the last modified timestamp."""
        self.updated_at = datetime.utcnow()
    
    @classmethod
    def create_mock_instrument(cls, name: str, channels: int = 8) -> "Instrument":
        """Create a mock instrument for testing."""
        return cls(
            id=f"mock_{name.lower().replace(' ', '_')}",
            name=name,
            instrument_type=InstrumentType.CYCLER,
            manufacturer="Mock",
            model="Simulator",
            serial_number="MOCK001",
            connection_string="mock://localhost",
            driver_name="mock",
            max_channels=channels,
            max_voltage=5.0,
            max_current=100.0,
            max_power=500.0,
            voltage_resolution=0.001,
            current_resolution=0.001,
            description=f"Mock {name} for testing and development",
            tags=["mock", "testing"],
        )
