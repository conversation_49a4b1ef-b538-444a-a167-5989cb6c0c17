[metadata]
name = battery-testing-system
version = attr: bts.__version__
description = Modular, extensible battery testing software for laboratories and manufacturing
long_description = file: README.md
long_description_content_type = text/markdown
url = https://github.com/bts-team/battery-testing-system
author = Battery Testing System Team
author_email = <EMAIL>
license = MIT
license_files = LICENSE
classifiers =
    Development Status :: 3 - Alpha
    Intended Audience :: Science/Research
    Intended Audience :: Manufacturing
    License :: OSI Approved :: MIT License
    Operating System :: OS Independent
    Programming Language :: Python :: 3
    Programming Language :: Python :: 3.9
    Programming Language :: Python :: 3.10
    Programming Language :: Python :: 3.11
    Programming Language :: Python :: 3.12
    Topic :: Scientific/Engineering
    Topic :: Scientific/Engineering :: Chemistry
    Topic :: System :: Hardware

[options]
package_dir =
    = src
packages = find:
python_requires = >=3.9
include_package_data = True
zip_safe = False

[options.packages.find]
where = src

[options.entry_points]
console_scripts =
    bts = bts.cli:main
    bts-gui = bts.gui:main
    bts-server = bts.web.server:main

bts.drivers =
    arbin = bts.infrastructure.drivers.arbin:ArbinDriver
    bitrode = bts.infrastructure.drivers.bitrode:BitrodeDriver
    maccor = bts.infrastructure.drivers.maccor:MaccorDriver
    neware = bts.infrastructure.drivers.neware:NewareDriver
    keithley = bts.infrastructure.drivers.keithley:KeithleyDriver
    mock = bts.infrastructure.drivers.mock:MockDriver

bts.test_profiles =
    cc_cv = bts.domain.test_profiles.cc_cv:CCCVProfile
    cp = bts.domain.test_profiles.cp:CPProfile
    eis = bts.domain.test_profiles.eis:EISProfile
    pulse = bts.domain.test_profiles.pulse:PulseProfile
    drive_cycle = bts.domain.test_profiles.drive_cycle:DriveCycleProfile

bts.exporters =
    csv = bts.infrastructure.exporters.csv:CSVExporter
    excel = bts.infrastructure.exporters.excel:ExcelExporter
    json = bts.infrastructure.exporters.json:JSONExporter
    influxdb = bts.infrastructure.exporters.influxdb:InfluxDBExporter

[options.package_data]
bts.gui.resources = *.qss, *.png, *.svg, *.ico
bts.templates = *.html, *.jinja2
bts.config = *.toml, *.yaml

[flake8]
max-line-length = 88
extend-ignore = E203, W503, E501
exclude =
    .git,
    __pycache__,
    .venv,
    .eggs,
    *.egg,
    build,
    dist,
    .tox

[tool:pytest]
minversion = 7.0
addopts = -ra -q --strict-markers --strict-config
testpaths = tests
python_files = test_*.py *_test.py
python_classes = Test*
python_functions = test_*
markers =
    slow: marks tests as slow (deselect with '-m "not slow"')
    integration: marks tests as integration tests
    hardware: marks tests that require hardware
    gui: marks tests that require GUI
asyncio_mode = auto
