"""Main CLI entry point for the Battery Testing System."""

import asyncio
import logging
import sys
from pathlib import Path
from typing import Optional

try:
    import click
    from rich.console import Console
    from rich.table import Table
    from rich.logging import RichHandler
    RICH_AVAILABLE = True
except ImportError:
    RICH_AVAILABLE = False
    click = None

from ..infrastructure.config.settings import Settings
from ..infrastructure.database.connection import DatabaseConnection
from ..application.services.hardware_manager import HardwareManager
from ..application.services.data_manager import DataManager
from ..application.services.safety_manager import SafetyManager
from ..application.services.test_engine import TestEngine


if RICH_AVAILABLE:
    console = Console()
else:
    console = None


def setup_logging(level: str = "INFO"):
    """Setup logging configuration."""
    if RICH_AVAILABLE:
        logging.basicConfig(
            level=getattr(logging, level.upper()),
            format="%(message)s",
            datefmt="[%X]",
            handlers=[<PERSON><PERSON><PERSON><PERSON>(console=console, rich_tracebacks=True)]
        )
    else:
        logging.basicConfig(
            level=getattr(logging, level.upper()),
            format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
        )


if click:
    @click.group()
    @click.option('--config', '-c', help='Configuration file path')
    @click.option('--log-level', default='INFO', help='Logging level')
    @click.option('--debug', is_flag=True, help='Enable debug mode')
    @click.pass_context
    def cli(ctx, config: Optional[str], log_level: str, debug: bool):
        """Battery Testing System CLI."""
        ctx.ensure_object(dict)
        
        # Load settings
        settings = Settings.load_from_file(config)
        if debug:
            settings.debug_mode = True
            log_level = "DEBUG"
        
        ctx.obj['settings'] = settings
        
        # Setup logging
        setup_logging(log_level)
    
    @cli.command()
    @click.pass_context
    def init(ctx):
        """Initialize the BTS database and configuration."""
        settings = ctx.obj['settings']
        
        if console:
            console.print("[bold blue]Initializing Battery Testing System...[/bold blue]")
        else:
            print("Initializing Battery Testing System...")
        
        async def _init():
            # Initialize database
            db = DatabaseConnection(settings)
            if await db.connect():
                if await db.create_tables():
                    if console:
                        console.print("[green]✓[/green] Database initialized successfully")
                    else:
                        print("✓ Database initialized successfully")
                else:
                    if console:
                        console.print("[red]✗[/red] Failed to create database tables")
                    else:
                        print("✗ Failed to create database tables")
                    return False
                await db.disconnect()
            else:
                if console:
                    console.print("[red]✗[/red] Failed to connect to database")
                else:
                    print("✗ Failed to connect to database")
                return False
            
            # Create default config file
            config_path = "bts.toml"
            if settings.save_to_file(config_path):
                if console:
                    console.print(f"[green]✓[/green] Configuration saved to {config_path}")
                else:
                    print(f"✓ Configuration saved to {config_path}")
            else:
                if console:
                    console.print(f"[yellow]![/yellow] Failed to save configuration to {config_path}")
                else:
                    print(f"! Failed to save configuration to {config_path}")
            
            return True
        
        success = asyncio.run(_init())
        if success:
            if console:
                console.print("[bold green]Initialization complete![/bold green]")
            else:
                print("Initialization complete!")
        else:
            sys.exit(1)
    
    @cli.command()
    @click.pass_context
    def status(ctx):
        """Show system status."""
        settings = ctx.obj['settings']
        
        async def _status():
            # Create services
            db = DatabaseConnection(settings)
            hardware_manager = HardwareManager()
            data_manager = DataManager(settings.database.dict())
            safety_manager = SafetyManager()
            test_engine = TestEngine(hardware_manager, data_manager, safety_manager)
            
            try:
                # Start services
                await db.connect()
                await hardware_manager.start()
                await data_manager.start()
                await safety_manager.start()
                await test_engine.start()
                
                # Get status information
                db_info = await db.get_connection_info()
                hw_status = await hardware_manager.get_system_status()
                
                if console:
                    # Create status table
                    table = Table(title="Battery Testing System Status")
                    table.add_column("Component", style="cyan")
                    table.add_column("Status", style="green")
                    table.add_column("Details")
                    
                    # Database status
                    db_status = "Connected" if db_info["connected"] else "Disconnected"
                    table.add_row("Database", db_status, f"Primary: {db_info['primary_url']}")
                    
                    # Hardware status
                    hw_status_str = f"{hw_status['connected_instruments']}/{hw_status['total_instruments']} connected"
                    table.add_row("Hardware", hw_status_str, f"{hw_status['available_channels']} channels available")
                    
                    # Services status
                    table.add_row("Test Engine", "Running" if test_engine.is_running else "Stopped", f"{test_engine.active_test_count} active tests")
                    table.add_row("Safety Manager", "Active" if safety_manager.safety_enabled else "Disabled", "")
                    
                    console.print(table)
                else:
                    print("Battery Testing System Status:")
                    print(f"  Database: {'Connected' if db_info['connected'] else 'Disconnected'}")
                    print(f"  Hardware: {hw_status['connected_instruments']}/{hw_status['total_instruments']} instruments connected")
                    print(f"  Channels: {hw_status['available_channels']} available")
                    print(f"  Test Engine: {'Running' if test_engine.is_running else 'Stopped'}")
                    print(f"  Active Tests: {test_engine.active_test_count}")
                
            finally:
                # Cleanup
                await test_engine.stop()
                await safety_manager.stop()
                await data_manager.stop()
                await hardware_manager.stop()
                await db.disconnect()
        
        asyncio.run(_status())
    
    @cli.command()
    @click.option('--host', default='0.0.0.0', help='Host to bind to')
    @click.option('--port', default=8000, help='Port to bind to')
    @click.pass_context
    def serve(ctx, host: str, port: int):
        """Start the BTS server."""
        settings = ctx.obj['settings']
        
        if console:
            console.print(f"[bold blue]Starting BTS server on {host}:{port}...[/bold blue]")
        else:
            print(f"Starting BTS server on {host}:{port}...")
        
        # This would start the FastAPI server
        # For now, just show a message
        if console:
            console.print("[yellow]Server implementation not yet available[/yellow]")
        else:
            print("Server implementation not yet available")
    
    @cli.command()
    @click.pass_context
    def gui(ctx):
        """Start the BTS GUI."""
        settings = ctx.obj['settings']
        
        if console:
            console.print("[bold blue]Starting BTS GUI...[/bold blue]")
        else:
            print("Starting BTS GUI...")
        
        # This would start the PySide6 GUI
        # For now, just show a message
        if console:
            console.print("[yellow]GUI implementation not yet available[/yellow]")
        else:
            print("GUI implementation not yet available")
    
    @cli.command()
    @click.option('--format', 'export_format', default='csv', help='Export format (csv, json, excel)')
    @click.option('--output', '-o', help='Output file path')
    @click.option('--test-id', help='Test ID to export')
    @click.pass_context
    def export(ctx, export_format: str, output: Optional[str], test_id: Optional[str]):
        """Export test data."""
        settings = ctx.obj['settings']
        
        if console:
            console.print(f"[bold blue]Exporting data in {export_format} format...[/bold blue]")
        else:
            print(f"Exporting data in {export_format} format...")
        
        async def _export():
            data_manager = DataManager(settings.database.dict())
            await data_manager.start()
            
            try:
                success = await data_manager.export_data(
                    format_type=export_format,
                    test_id=test_id,
                    output_path=output
                )
                
                if success:
                    if console:
                        console.print("[green]✓[/green] Export completed successfully")
                    else:
                        print("✓ Export completed successfully")
                else:
                    if console:
                        console.print("[red]✗[/red] Export failed")
                    else:
                        print("✗ Export failed")
                    
            finally:
                await data_manager.stop()
        
        asyncio.run(_export())
    
    def main():
        """Main CLI entry point."""
        if not click:
            print("Error: click and rich packages are required for CLI functionality")
            print("Install with: pip install click rich")
            sys.exit(1)
        
        cli()

else:
    def main():
        """Fallback main function when click is not available."""
        print("Error: click package is required for CLI functionality")
        print("Install with: pip install click rich")
        sys.exit(1)


if __name__ == "__main__":
    main()
