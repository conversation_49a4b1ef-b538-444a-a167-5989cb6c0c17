"""Base abstract class for instrument drivers."""

import asyncio
import logging
from abc import ABC, abstractmethod
from dataclasses import dataclass
from datetime import datetime
from enum import Enum
from typing import Dict, Any, Optional, List, Union
from uuid import uuid4

from ...domain.entities.measurement import Measurement
from ...domain.entities.instrument import Instrument, InstrumentState


class ConnectionType(Enum):
    """Types of instrument connections."""
    SERIAL = "serial"
    ETHERNET = "ethernet"
    USB = "usb"
    MODBUS_TCP = "modbus_tcp"
    MODBUS_RTU = "modbus_rtu"
    CANBUS = "canbus"
    NIDAQMX = "nidaqmx"
    MOCK = "mock"


@dataclass
class DriverCapabilities:
    """Describes the capabilities of an instrument driver."""
    
    # Basic capabilities
    supports_voltage_control: bool = False
    supports_current_control: bool = False
    supports_power_control: bool = False
    supports_resistance_control: bool = False
    
    # Measurement capabilities
    can_measure_voltage: bool = False
    can_measure_current: bool = False
    can_measure_temperature: bool = False
    can_measure_power: bool = False
    can_measure_resistance: bool = False
    
    # Advanced features
    supports_eis: bool = False  # Electrochemical Impedance Spectroscopy
    supports_pulse: bool = False
    supports_data_logging: bool = False
    supports_safety_limits: bool = False
    
    # Channel capabilities
    max_channels: int = 1
    independent_channels: bool = True
    
    # Ranges and resolution
    voltage_range: tuple[float, float] = (0.0, 5.0)
    current_range: tuple[float, float] = (-100.0, 100.0)
    voltage_resolution: float = 0.001
    current_resolution: float = 0.001
    
    # Timing
    min_sampling_interval: float = 0.1  # seconds
    max_sampling_interval: float = 3600.0  # seconds


class InstrumentDriver(ABC):
    """
    Abstract base class for all instrument drivers.
    
    This class defines the interface that all instrument drivers must implement
    to provide a consistent API for battery testing operations.
    """
    
    def __init__(self, instrument: Instrument):
        """Initialize the driver with an instrument configuration."""
        self.instrument = instrument
        self.logger = logging.getLogger(f"{self.__class__.__name__}_{instrument.id}")
        self._connected = False
        self._channels_in_use: set[int] = set()
        self._last_measurements: Dict[int, Measurement] = {}
        
    @property
    @abstractmethod
    def capabilities(self) -> DriverCapabilities:
        """Get the capabilities of this driver."""
        pass
    
    @property
    @abstractmethod
    def connection_type(self) -> ConnectionType:
        """Get the connection type for this driver."""
        pass
    
    @property
    def is_connected(self) -> bool:
        """Check if the driver is connected to the instrument."""
        return self._connected
    
    @property
    def available_channels(self) -> List[int]:
        """Get list of available (not in use) channels."""
        max_channels = self.capabilities.max_channels
        all_channels = set(range(1, max_channels + 1))
        return list(all_channels - self._channels_in_use)
    
    @abstractmethod
    async def connect(self) -> bool:
        """
        Connect to the instrument.
        
        Returns:
            bool: True if connection successful, False otherwise
        """
        pass
    
    @abstractmethod
    async def disconnect(self) -> bool:
        """
        Disconnect from the instrument.
        
        Returns:
            bool: True if disconnection successful, False otherwise
        """
        pass
    
    @abstractmethod
    async def identify(self) -> Dict[str, str]:
        """
        Get instrument identification information.
        
        Returns:
            Dict containing manufacturer, model, serial_number, firmware_version
        """
        pass
    
    @abstractmethod
    async def reset(self) -> bool:
        """
        Reset the instrument to default state.
        
        Returns:
            bool: True if reset successful, False otherwise
        """
        pass
    
    @abstractmethod
    async def get_status(self) -> Dict[str, Any]:
        """
        Get current instrument status.
        
        Returns:
            Dict containing status information
        """
        pass
    
    # Channel management
    @abstractmethod
    async def get_channel_count(self) -> int:
        """Get the number of available channels."""
        pass
    
    @abstractmethod
    async def enable_channel(self, channel: int) -> bool:
        """
        Enable a specific channel.
        
        Args:
            channel: Channel number to enable
            
        Returns:
            bool: True if successful, False otherwise
        """
        pass
    
    @abstractmethod
    async def disable_channel(self, channel: int) -> bool:
        """
        Disable a specific channel.
        
        Args:
            channel: Channel number to disable
            
        Returns:
            bool: True if successful, False otherwise
        """
        pass
    
    # Control methods
    @abstractmethod
    async def set_voltage(self, channel: int, voltage: float) -> bool:
        """
        Set voltage on a channel.
        
        Args:
            channel: Channel number
            voltage: Target voltage in volts
            
        Returns:
            bool: True if successful, False otherwise
        """
        pass
    
    @abstractmethod
    async def set_current(self, channel: int, current: float) -> bool:
        """
        Set current on a channel.
        
        Args:
            channel: Channel number
            current: Target current in amperes
            
        Returns:
            bool: True if successful, False otherwise
        """
        pass
    
    @abstractmethod
    async def set_power(self, channel: int, power: float) -> bool:
        """
        Set power on a channel.
        
        Args:
            channel: Channel number
            power: Target power in watts
            
        Returns:
            bool: True if successful, False otherwise
        """
        pass
    
    # Measurement methods
    @abstractmethod
    async def read_measurement(self, channel: int) -> Optional[Measurement]:
        """
        Read a measurement from a channel.
        
        Args:
            channel: Channel number to read from
            
        Returns:
            Measurement object or None if failed
        """
        pass
    
    @abstractmethod
    async def read_all_measurements(self) -> Dict[int, Measurement]:
        """
        Read measurements from all active channels.
        
        Returns:
            Dict mapping channel numbers to Measurement objects
        """
        pass
    
    # Safety and limits
    async def set_safety_limits(
        self,
        channel: int,
        max_voltage: Optional[float] = None,
        max_current: Optional[float] = None,
        max_power: Optional[float] = None,
        max_temperature: Optional[float] = None,
    ) -> bool:
        """
        Set safety limits for a channel.
        
        Args:
            channel: Channel number
            max_voltage: Maximum voltage limit
            max_current: Maximum current limit
            max_power: Maximum power limit
            max_temperature: Maximum temperature limit
            
        Returns:
            bool: True if successful, False otherwise
        """
        # Default implementation - override in subclasses if supported
        self.logger.warning("Safety limits not supported by this driver")
        return False
    
    # Advanced features
    async def start_eis(
        self,
        channel: int,
        frequency_range: tuple[float, float],
        amplitude: float,
        points: int = 50,
    ) -> bool:
        """
        Start Electrochemical Impedance Spectroscopy measurement.
        
        Args:
            channel: Channel number
            frequency_range: (min_freq, max_freq) in Hz
            amplitude: AC amplitude in volts
            points: Number of frequency points
            
        Returns:
            bool: True if successful, False otherwise
        """
        # Default implementation - override in subclasses if supported
        self.logger.warning("EIS not supported by this driver")
        return False
    
    async def start_pulse(
        self,
        channel: int,
        pulse_current: float,
        pulse_duration: float,
        rest_duration: float,
        pulse_count: int = 1,
    ) -> bool:
        """
        Start pulse testing.
        
        Args:
            channel: Channel number
            pulse_current: Pulse current in amperes
            pulse_duration: Pulse duration in seconds
            rest_duration: Rest duration between pulses in seconds
            pulse_count: Number of pulses
            
        Returns:
            bool: True if successful, False otherwise
        """
        # Default implementation - override in subclasses if supported
        self.logger.warning("Pulse testing not supported by this driver")
        return False
    
    # Utility methods
    def reserve_channel(self, channel: int) -> bool:
        """Reserve a channel for exclusive use."""
        if channel in self._channels_in_use:
            return False
        
        if channel < 1 or channel > self.capabilities.max_channels:
            return False
        
        self._channels_in_use.add(channel)
        return True
    
    def release_channel(self, channel: int) -> bool:
        """Release a reserved channel."""
        if channel in self._channels_in_use:
            self._channels_in_use.remove(channel)
            return True
        return False
    
    def get_last_measurement(self, channel: int) -> Optional[Measurement]:
        """Get the last measurement for a channel."""
        return self._last_measurements.get(channel)
    
    def _store_measurement(self, channel: int, measurement: Measurement) -> None:
        """Store a measurement for later retrieval."""
        self._last_measurements[channel] = measurement
    
    async def _validate_channel(self, channel: int) -> bool:
        """Validate that a channel number is valid."""
        if channel < 1 or channel > self.capabilities.max_channels:
            self.logger.error(f"Invalid channel number: {channel}")
            return False
        return True
    
    async def _validate_voltage(self, voltage: float) -> bool:
        """Validate that a voltage is within range."""
        min_v, max_v = self.capabilities.voltage_range
        if not (min_v <= voltage <= max_v):
            self.logger.error(f"Voltage {voltage}V outside range [{min_v}, {max_v}]")
            return False
        return True
    
    async def _validate_current(self, current: float) -> bool:
        """Validate that a current is within range."""
        min_i, max_i = self.capabilities.current_range
        if not (min_i <= current <= max_i):
            self.logger.error(f"Current {current}A outside range [{min_i}, {max_i}]")
            return False
        return True
    
    def __str__(self) -> str:
        """String representation of the driver."""
        return f"{self.__class__.__name__}({self.instrument.name})"
    
    def __repr__(self) -> str:
        """Detailed string representation of the driver."""
        return (f"{self.__class__.__name__}("
                f"instrument_id='{self.instrument.id}', "
                f"connected={self.is_connected}, "
                f"channels_in_use={len(self._channels_in_use)})")
    
    async def __aenter__(self):
        """Async context manager entry."""
        await self.connect()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit."""
        await self.disconnect()
