pydantic>=2.0.0
pydantic-settings>=2.0.0
sqlalchemy>=2.0.0
alembic>=1.12.0
asyncio-mqtt>=0.16.0
aiofiles>=23.0.0
httpx>=0.25.0
PySide6>=6.5.0
pyqtgraph>=0.13.0
qdarkstyle>=3.2.0
pandas>=2.0.0
numpy>=1.24.0
scipy>=1.11.0
h5py>=3.9.0
openpyxl>=3.1.0
xlsxwriter>=3.1.0
aiosqlite>=0.19.0
asyncpg>=0.28.0
influxdb-client[async]>=1.38.0
pymodbus>=3.5.0
pyserial>=3.5
pyserial-asyncio>=0.6
python-can>=4.2.0
fastapi>=0.104.0
uvicorn[standard]>=0.24.0
websockets>=12.0
toml>=0.10.2
pyyaml>=6.0
click>=8.1.0
rich>=13.6.0
structlog>=23.2.0
python-multipart>=0.0.6
passlib[bcrypt]>=1.7.4
python-jose[cryptography]>=3.3.0
jinja2>=3.1.0
weasyprint>=60.0
matplotlib>=3.7.0
plotly>=5.17.0

[:platform_system == "Windows"]
nidaqmx>=0.9.0

[dev]
pytest>=7.4.0
pytest-asyncio>=0.21.0
pytest-cov>=4.1.0
pytest-mock>=3.11.0
black>=23.9.0
isort>=5.12.0
flake8>=6.1.0
mypy>=1.6.0
pre-commit>=3.5.0

[docs]
sphinx>=7.2.0
sphinx-rtd-theme>=1.3.0
myst-parser>=2.0.0

[test]
pytest>=7.4.0
pytest-asyncio>=0.21.0
pytest-cov>=4.1.0
pytest-mock>=3.11.0
factory-boy>=3.3.0
faker>=19.12.0
