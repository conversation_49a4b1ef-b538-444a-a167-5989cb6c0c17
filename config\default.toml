# Battery Testing System - Default Configuration

[system]
# System-wide settings
name = "Battery Testing System"
version = "0.1.0"
data_retention_days = 365
max_channels = 1000
sampling_rate_hz = 1.0
log_level = "INFO"
debug_mode = false

[database]
# Database configuration
primary = "sqlite:///data/bts.db"
timeseries = "sqlite:///data/bts_timeseries.db"
# Alternative configurations:
# primary = "postgresql://user:pass@localhost:5432/bts"
# timeseries = "influxdb://localhost:8086/bts"

[database.connection_pool]
max_connections = 20
min_connections = 5
connection_timeout = 30
retry_attempts = 3

[safety]
# Global safety limits
global_voltage_limit = 5.0
global_current_limit = 100.0
temperature_limit = 60.0
pressure_limit = 2.0
emergency_stop_enabled = true
safety_check_interval = 1.0

[safety.alarms]
fire_alarm_enabled = false
fire_alarm_protocol = "modbus_tcp"
fire_alarm_address = "*************:502"

[hardware]
# Hardware discovery and communication
auto_discovery = true
discovery_interval = 30
connection_timeout = 30
retry_attempts = 3
hot_plug_detection = true

[hardware.modbus]
timeout = 5.0
retries = 3
unit_id = 1

[hardware.canbus]
interface = "socketcan"
channel = "can0"
bitrate = 500000

[hardware.serial]
baudrate = 9600
timeout = 1.0
parity = "N"
stopbits = 1
bytesize = 8

[gui]
# GUI configuration
theme = "dark"
language = "en"
auto_save_interval = 300
plot_buffer_size = 10000
update_interval = 100

[gui.plots]
default_colors = ["#1f77b4", "#ff7f0e", "#2ca02c", "#d62728", "#9467bd"]
line_width = 2
grid_enabled = true
legend_enabled = true

[web]
# Web interface configuration
enabled = false
host = "0.0.0.0"
port = 8000
cors_origins = ["http://localhost:3000"]
api_prefix = "/api/v1"

[auth]
# Authentication and authorization
enabled = true
session_timeout = 3600
password_min_length = 8
require_password_complexity = true
max_login_attempts = 5
lockout_duration = 300

[auth.jwt]
secret_key = "your-secret-key-change-this"
algorithm = "HS256"
access_token_expire_minutes = 30

[logging]
# Logging configuration
level = "INFO"
format = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
file_enabled = true
file_path = "logs/bts.log"
file_max_size = "10MB"
file_backup_count = 5
console_enabled = true

[data_export]
# Data export settings
default_format = "csv"
include_metadata = true
timestamp_format = "ISO8601"
decimal_places = 6

[data_export.csv]
delimiter = ","
quote_char = "\""
line_terminator = "\n"

[plugins]
# Plugin system configuration
auto_load = true
plugin_directories = ["plugins", "~/.bts/plugins"]
enable_third_party = true

[performance]
# Performance tuning
max_worker_threads = 4
data_buffer_size = 1000
batch_write_size = 100
gc_threshold = 1000

[notifications]
# Notification settings
email_enabled = false
smtp_server = "localhost"
smtp_port = 587
smtp_username = ""
smtp_password = ""
from_address = "bts@localhost"

[backup]
# Backup configuration
auto_backup = true
backup_interval_hours = 24
backup_retention_days = 30
backup_location = "backups/"
compress_backups = true
