# Installation Guide

This guide covers different installation methods for the Battery Testing System (BTS).

## System Requirements

### Minimum Requirements
- **Operating System**: Windows 10, Linux (Ubuntu 18.04+), macOS 10.14+
- **Python**: 3.9 or higher
- **Memory**: 4GB RAM
- **Storage**: 1GB free space for installation
- **Network**: Internet connection for package downloads

### Recommended Requirements
- **Memory**: 8GB RAM or more
- **Storage**: 10GB+ for data storage
- **CPU**: Multi-core processor for better performance
- **Network**: Gigabit Ethernet for high-speed instrument communication

### Hardware Requirements (Optional)
- **Instruments**: Supported battery cyclers, SMUs, or DAQ devices
- **Network**: Ethernet connection for networked instruments
- **Serial**: USB-to-serial adapters for serial instruments

## Installation Methods

### Method 1: PyPI Installation (Recommended)

The easiest way to install BTS:

```bash
# Install latest stable version
pip install battery-testing-system

# Install with optional dependencies
pip install battery-testing-system[gui,web,hardware]

# Verify installation
bts --version
```

### Method 2: Source Installation

For development or latest features:

```bash
# Clone repository
git clone https://github.com/bts-team/battery-testing-system.git
cd battery-testing-system

# Create virtual environment (recommended)
python -m venv bts-env
source bts-env/bin/activate  # On Windows: bts-env\Scripts\activate

# Install in development mode
pip install -e .

# Install with all optional dependencies
pip install -e ".[dev,test,docs,gui,web,hardware]"
```

### Method 3: Docker Installation

For containerized deployment:

```bash
# Pull official image
docker pull bts/battery-testing-system:latest

# Run with data persistence
docker run -d \
  --name bts-server \
  -p 8000:8000 \
  -v $(pwd)/bts-data:/app/data \
  -v $(pwd)/bts-config:/app/config \
  bts/battery-testing-system:latest

# Access web interface at http://localhost:8000
```

### Method 4: Conda Installation

Using conda package manager:

```bash
# Add conda-forge channel
conda config --add channels conda-forge

# Install BTS
conda install battery-testing-system

# Or create dedicated environment
conda create -n bts-env python=3.11 battery-testing-system
conda activate bts-env
```

## Platform-Specific Instructions

### Windows

#### Prerequisites
```powershell
# Install Python from python.org or Microsoft Store
# Ensure Python is in PATH

# Install Git (optional, for source installation)
# Download from: https://git-scm.com/download/win

# Install Visual Studio Build Tools (for some dependencies)
# Download from: https://visualstudio.microsoft.com/visual-cpp-build-tools/
```

#### Installation
```powershell
# Open PowerShell or Command Prompt
pip install battery-testing-system

# For GUI support
pip install battery-testing-system[gui]

# Initialize system
bts init

# Start GUI
bts gui
```

#### Windows-Specific Features
- **NI-DAQmx**: Automatic support for National Instruments hardware
- **COM Ports**: Automatic detection of serial instruments
- **Windows Services**: Option to run as Windows service

### Linux (Ubuntu/Debian)

#### Prerequisites
```bash
# Update package list
sudo apt update

# Install Python and pip
sudo apt install python3 python3-pip python3-venv

# Install development tools (for source installation)
sudo apt install git build-essential python3-dev

# Install system dependencies for GUI
sudo apt install python3-tk libxcb-xinerama0

# For hardware support
sudo apt install libusb-1.0-0-dev libudev-dev
```

#### Installation
```bash
# Create virtual environment
python3 -m venv ~/bts-env
source ~/bts-env/bin/activate

# Install BTS
pip install battery-testing-system

# Initialize system
bts init

# Start web server
bts serve
```

#### Linux-Specific Configuration
```bash
# Add user to dialout group for serial access
sudo usermod -a -G dialout $USER

# Set up udev rules for USB instruments
sudo tee /etc/udev/rules.d/99-bts-instruments.rules << EOF
# BTS Instrument Access
SUBSYSTEM=="usb", ATTRS{idVendor}=="05e6", MODE="0666"
SUBSYSTEM=="tty", ATTRS{idVendor}=="0403", MODE="0666"
EOF

# Reload udev rules
sudo udevadm control --reload-rules
sudo udevadm trigger
```

### macOS

#### Prerequisites
```bash
# Install Homebrew (if not already installed)
/bin/bash -c "$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)"

# Install Python
brew install python

# Install development tools (for source installation)
brew install git
```

#### Installation
```bash
# Create virtual environment
python3 -m venv ~/bts-env
source ~/bts-env/bin/activate

# Install BTS
pip install battery-testing-system

# Initialize system
bts init

# Start GUI (requires XQuartz for some features)
bts gui
```

## Optional Dependencies

### GUI Dependencies
For desktop GUI support:
```bash
pip install PySide6 pyqtgraph qdarkstyle
```

### Web Dependencies
For web interface:
```bash
pip install fastapi uvicorn websockets
```

### Hardware Dependencies
For specific hardware support:
```bash
# Modbus communication
pip install pymodbus

# Serial communication
pip install pyserial pyserial-asyncio

# CANbus support
pip install python-can

# National Instruments (Windows only)
pip install nidaqmx

# InfluxDB support
pip install influxdb-client
```

### Development Dependencies
For development and testing:
```bash
pip install pytest pytest-asyncio pytest-cov black isort flake8 mypy
```

## Post-Installation Setup

### 1. Initialize System
```bash
# Create database and default configuration
bts init

# Verify installation
bts status
```

### 2. Configure Database (Optional)
Edit `bts.toml` for production database:
```toml
[database]
primary = "postgresql://user:pass@localhost:5432/bts"
timeseries = "influxdb://localhost:8086/bts"
```

### 3. Set Up Hardware Drivers
```bash
# Install specific drivers as needed
pip install bts-driver-arbin
pip install bts-driver-bitrode
pip install bts-driver-keithley
```

### 4. Configure Safety Settings
Edit safety limits in `bts.toml`:
```toml
[safety]
global_voltage_limit = 5.0
global_current_limit = 100.0
temperature_limit = 60.0
emergency_stop_enabled = true
```

## Verification

### Test Installation
```bash
# Check version
bts --version

# Run system check
bts status

# Test with mock hardware
python -c "
import asyncio
from bts.infrastructure.drivers.mock import MockDriver
from bts.domain.entities.instrument import Instrument

async def test():
    instrument = Instrument.create_mock_instrument('Test', 4)
    driver = MockDriver(instrument)
    success = await driver.connect()
    print(f'Mock driver test: {\"PASS\" if success else \"FAIL\"}')
    await driver.disconnect()

asyncio.run(test())
"
```

### Run Tests
```bash
# Run basic tests (if installed from source)
pytest tests/unit/test_domain_entities.py -v

# Run integration tests
pytest tests/integration/ -v
```

## Troubleshooting

### Common Issues

#### Import Errors
```bash
# Error: ModuleNotFoundError: No module named 'bts'
# Solution: Ensure BTS is installed in active environment
pip list | grep battery-testing-system

# Reinstall if necessary
pip uninstall battery-testing-system
pip install battery-testing-system
```

#### Permission Errors
```bash
# Linux/macOS: Permission denied for serial ports
sudo usermod -a -G dialout $USER
# Log out and back in

# Windows: Access denied for COM ports
# Run as administrator or adjust COM port permissions
```

#### Database Errors
```bash
# Error: Database connection failed
# Check database configuration in bts.toml
# Ensure database server is running (for PostgreSQL/InfluxDB)

# Reset to SQLite
bts init --reset-db
```

#### GUI Issues
```bash
# Linux: GUI doesn't start
sudo apt install python3-tk libxcb-xinerama0

# macOS: GUI rendering issues
brew install --cask xquartz

# Windows: GUI crashes
# Update graphics drivers
# Install Visual C++ Redistributable
```

### Getting Help

If you encounter issues:

1. **Check logs**: Look in `logs/bts.log` for error messages
2. **Verify dependencies**: Run `pip check` to verify package consistency
3. **Update packages**: Run `pip install --upgrade battery-testing-system`
4. **Check documentation**: Review relevant sections in this guide
5. **Search issues**: Check [GitHub Issues](https://github.com/bts-team/battery-testing-system/issues)
6. **Ask for help**: Post in [GitHub Discussions](https://github.com/bts-team/battery-testing-system/discussions)

## Uninstallation

### Remove BTS
```bash
# Uninstall package
pip uninstall battery-testing-system

# Remove data (optional)
rm -rf ~/.bts/
rm -rf ./data/
rm -rf ./logs/
rm bts.toml
```

### Docker Cleanup
```bash
# Stop and remove container
docker stop bts-server
docker rm bts-server

# Remove image
docker rmi bts/battery-testing-system
```

## Next Steps

After successful installation:

1. **[Getting Started Guide](getting_started.md)** - Run your first test
2. **[Hardware Setup](hardware_setup.md)** - Configure your instruments
3. **[Configuration Guide](configuration.md)** - Customize BTS settings
4. **[User Manual](user_manual.md)** - Complete feature overview

---

**Need help?** Contact <NAME_EMAIL> or visit our [documentation site](https://battery-testing-system.readthedocs.io/).
