#!/usr/bin/env python3
"""
Basic Battery Test Example

This example demonstrates how to:
1. Set up a mock instrument
2. Create a simple test profile
3. Run a battery test
4. Monitor progress and export results

This is a complete working example that can be run without any hardware.
"""

import asyncio
import logging
from datetime import datetime
from pathlib import Path

# BTS imports
from bts.infrastructure.config.settings import Settings
from bts.infrastructure.database.connection import DatabaseConnection
from bts.application.services.hardware_manager import HardwareManager
from bts.application.services.data_manager import DataManager
from bts.application.services.safety_manager import SafetyManager
from bts.application.services.test_engine import TestEngine

from bts.domain.entities.instrument import Instrument, InstrumentType
from bts.domain.entities.test_profile import TestProfile, TestProfileType
from bts.domain.entities.test import Test, TestStep, StepType
from bts.domain.value_objects.safety_limits import SafetyLimits


# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


async def create_test_profile() -> TestProfile:
    """Create a simple CC-CV test profile."""
    logger.info("Creating test profile...")
    
    # Define test steps
    steps = [
        TestStep(
            step_number=1,
            name="CC Charge",
            step_type=StepType.CC,
            target_value=1.0,        # 1A charge current
            limit_value=4.2,         # Stop at 4.2V
            limit_type="voltage",
            duration=3600,           # Max 1 hour
            description="Charge at 1A until 4.2V or 1 hour"
        ),
        TestStep(
            step_number=2,
            name="CV Charge",
            step_type=StepType.CV,
            target_value=4.2,        # Hold 4.2V
            limit_value=0.05,        # Stop at 50mA
            limit_type="current",
            duration=7200,           # Max 2 hours
            description="Hold 4.2V until current drops to 50mA or 2 hours"
        ),
        TestStep(
            step_number=3,
            name="Rest",
            step_type=StepType.REST,
            duration=300,            # 5 minutes
            description="Rest for 5 minutes"
        ),
        TestStep(
            step_number=4,
            name="CC Discharge",
            step_type=StepType.CC,
            target_value=-2.0,       # 2A discharge
            limit_value=2.5,         # Stop at 2.5V
            limit_type="voltage",
            duration=3600,           # Max 1 hour
            description="Discharge at 2A until 2.5V or 1 hour"
        ),
        TestStep(
            step_number=5,
            name="Final Rest",
            step_type=StepType.REST,
            duration=300,            # 5 minutes
            description="Final rest for 5 minutes"
        )
    ]
    
    # Create safety limits
    safety_limits = SafetyLimits(
        max_voltage=4.5,
        min_voltage=2.0,
        max_current=10.0,
        max_temperature=45.0,
        min_temperature=0.0,
        max_soc=95.0,
        min_soc=5.0,
    )
    
    # Create test profile
    profile = TestProfile(
        name="Basic Li-ion CC-CV Cycle",
        description="Standard lithium-ion battery charge/discharge cycle",
        profile_type=TestProfileType.CC_CV,
        steps=steps,
        default_cycles=3,
        safety_limits=safety_limits,
        category="Standard",
        tags=["li-ion", "cc-cv", "basic", "example"],
        author="BTS Example",
    )
    
    logger.info(f"Created profile '{profile.name}' with {len(profile.steps)} steps")
    return profile


async def setup_mock_instrument() -> Instrument:
    """Set up a mock instrument for testing."""
    logger.info("Setting up mock instrument...")
    
    instrument = Instrument.create_mock_instrument(
        name="Example Battery Cycler",
        channels=8
    )
    
    # Customize instrument properties
    instrument.description = "Mock 8-channel battery cycler for demonstration"
    instrument.location = "Example Laboratory"
    instrument.tags = ["mock", "demo", "8-channel"]
    
    logger.info(f"Created mock instrument '{instrument.name}' with {instrument.max_channels} channels")
    return instrument


async def run_battery_test():
    """Main function to run a complete battery test."""
    logger.info("Starting Battery Testing System example...")
    
    # Create data directory
    data_dir = Path("example_data")
    data_dir.mkdir(exist_ok=True)
    
    # Load settings with custom database path
    settings = Settings()
    settings.database.primary = f"sqlite:///{data_dir}/example_bts.db"
    settings.database.timeseries = f"sqlite:///{data_dir}/example_timeseries.db"
    settings.debug_mode = True
    
    # Initialize database
    logger.info("Initializing database...")
    db = DatabaseConnection(settings)
    await db.connect()
    await db.create_tables()
    
    # Create services
    logger.info("Starting services...")
    hardware_manager = HardwareManager()
    data_manager = DataManager(settings.database.dict())
    safety_manager = SafetyManager()
    test_engine = TestEngine(hardware_manager, data_manager, safety_manager)
    
    try:
        # Start all services
        await hardware_manager.start()
        await data_manager.start()
        await safety_manager.start()
        await test_engine.start()
        
        # Set up instrument
        instrument = await setup_mock_instrument()
        await hardware_manager.add_instrument(instrument)
        await hardware_manager.connect_instrument(instrument.id)
        
        # Verify connection
        connected_instruments = hardware_manager.get_connected_instruments()
        logger.info(f"Connected instruments: {len(connected_instruments)}")
        
        # Get available channels
        channels = hardware_manager.get_available_channels()
        if not channels:
            logger.error("No channels available!")
            return
        
        channel = channels[0]
        logger.info(f"Using channel: {channel.id}")
        
        # Create test profile and test
        profile = await create_test_profile()
        
        test = Test(
            name="Example Battery Test",
            description="Demonstration of BTS capabilities using mock hardware",
            profile_id=profile.id,
            steps=profile.steps.copy(),
            total_cycles=1,  # Single cycle for demo
            safety_limits=profile.safety_limits,
            tags=["example", "demo"],
        )
        
        logger.info(f"Created test '{test.name}' with {len(test.steps)} steps")
        
        # Save test to database
        await data_manager.save_test(test)
        
        # Start test
        logger.info("Starting test execution...")
        success = await test_engine.start_test(test, channel.id)
        
        if not success:
            logger.error("Failed to start test!")
            return
        
        logger.info("Test started successfully!")
        
        # Monitor test progress
        last_progress = -1
        measurement_count = 0
        
        while test.is_running:
            await asyncio.sleep(2)  # Check every 2 seconds
            
            # Get test status
            status = await test_engine.get_test_status(test.id)
            if status:
                progress = status['progress']
                current_step = status['current_step']
                
                # Log progress updates
                if abs(progress - last_progress) >= 5.0:  # Log every 5% progress
                    logger.info(f"Progress: {progress:.1f}% - Step {current_step}")
                    last_progress = progress
                
                # Log measurement info
                if status['last_measurement']:
                    measurement_count += 1
                    if measurement_count % 10 == 0:  # Log every 10th measurement
                        meas = status['last_measurement']
                        logger.info(
                            f"Measurement #{measurement_count}: "
                            f"V={meas.get('voltage', 'N/A'):.3f}V, "
                            f"I={meas.get('current', 'N/A'):.3f}A, "
                            f"T={meas.get('temperature', 'N/A'):.1f}°C"
                        )
            
            # Stop test after 60 seconds for demo purposes
            if test.duration and test.duration > 60:
                logger.info("Stopping test after 60 seconds (demo limit)")
                await test_engine.stop_test(test.id)
                break
        
        # Test completed
        final_status = await test_engine.get_test_status(test.id)
        if final_status:
            logger.info(f"Test completed with state: {final_status['state']}")
            logger.info(f"Final progress: {final_status['progress']:.1f}%")
        
        # Export test data
        logger.info("Exporting test data...")
        export_file = data_dir / f"test_results_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv"
        
        export_success = await data_manager.export_data(
            format_type="csv",
            test_id=test.id,
            output_path=str(export_file)
        )
        
        if export_success:
            logger.info(f"Test data exported to: {export_file}")
        else:
            logger.warning("Failed to export test data")
        
        # Display summary
        logger.info("\n" + "="*50)
        logger.info("TEST SUMMARY")
        logger.info("="*50)
        logger.info(f"Test Name: {test.name}")
        logger.info(f"Profile: {profile.name}")
        logger.info(f"Channel: {channel.id}")
        logger.info(f"Duration: {test.duration:.1f} seconds" if test.duration else "Duration: N/A")
        logger.info(f"Measurements: {measurement_count}")
        logger.info(f"Final State: {test.state.value}")
        logger.info(f"Data Directory: {data_dir.absolute()}")
        logger.info("="*50)
        
    except Exception as e:
        logger.error(f"Error during test execution: {e}", exc_info=True)
    
    finally:
        # Clean up services
        logger.info("Shutting down services...")
        await test_engine.stop()
        await safety_manager.stop()
        await data_manager.stop()
        await hardware_manager.stop()
        await db.disconnect()
        
        logger.info("Example completed!")


def main():
    """Entry point for the example."""
    print("Battery Testing System - Basic Test Example")
    print("=" * 50)
    print("This example demonstrates:")
    print("• Setting up a mock instrument")
    print("• Creating a test profile")
    print("• Running a battery test")
    print("• Monitoring progress")
    print("• Exporting results")
    print("=" * 50)
    print()
    
    try:
        asyncio.run(run_battery_test())
    except KeyboardInterrupt:
        print("\nExample interrupted by user")
    except Exception as e:
        print(f"\nExample failed with error: {e}")
        raise


if __name__ == "__main__":
    main()
