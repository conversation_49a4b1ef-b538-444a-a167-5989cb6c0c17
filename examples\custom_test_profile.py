#!/usr/bin/env python3
"""
Custom Test Profile Example

This example shows how to create advanced test profiles including:
1. EIS (Electrochemical Impedance Spectroscopy) testing
2. Pulse testing for power characterization
3. Drive cycle replay
4. Calendar aging simulation
5. Custom test steps with complex end conditions

These examples demonstrate the flexibility of the BTS test profile system.
"""

import asyncio
import logging
from datetime import datetime, timedelta
from typing import List, Dict, Any

from bts.domain.entities.test_profile import TestProfile, TestProfileType
from bts.domain.entities.test import TestStep, StepType
from bts.domain.value_objects.safety_limits import SafetyLimits


logger = logging.getLogger(__name__)


def create_eis_profile() -> TestProfile:
    """Create an EIS (Electrochemical Impedance Spectroscopy) test profile."""
    
    steps = [
        # Pre-conditioning
        TestStep(
            step_number=1,
            name="Pre-condition Charge",
            step_type=StepType.CC,
            target_value=0.5,  # C/2 charge
            limit_value=4.1,   # 4.1V limit
            limit_type="voltage",
            duration=7200,     # 2 hours max
            description="Pre-condition charge to 4.1V at C/2"
        ),
        
        TestStep(
            step_number=2,
            name="Rest Before EIS",
            step_type=StepType.REST,
            duration=1800,     # 30 minutes
            description="Rest to reach equilibrium before EIS"
        ),
        
        # EIS at different SOC levels
        TestStep(
            step_number=3,
            name="EIS at 100% SOC",
            step_type=StepType.EIS,
            target_value=4.1,  # DC bias voltage
            metadata={
                "frequency_range": [0.01, 10000],  # 10mHz to 10kHz
                "amplitude": 0.01,  # 10mV AC amplitude
                "points_per_decade": 10,
                "soc_target": 100
            },
            description="EIS measurement at 100% SOC"
        ),
        
        # Partial discharge for next SOC
        TestStep(
            step_number=4,
            name="Discharge to 75% SOC",
            step_type=StepType.CC,
            target_value=-0.2,  # C/5 discharge
            limit_value=0.75,   # Stop at 75% SOC
            limit_type="soc",
            description="Discharge to 75% SOC for next EIS"
        ),
        
        TestStep(
            step_number=5,
            name="Rest at 75% SOC",
            step_type=StepType.REST,
            duration=900,      # 15 minutes
            description="Rest at 75% SOC"
        ),
        
        TestStep(
            step_number=6,
            name="EIS at 75% SOC",
            step_type=StepType.EIS,
            target_value=3.8,  # Approximate voltage at 75% SOC
            metadata={
                "frequency_range": [0.01, 10000],
                "amplitude": 0.01,
                "points_per_decade": 10,
                "soc_target": 75
            },
            description="EIS measurement at 75% SOC"
        ),
        
        # Continue for 50% and 25% SOC
        TestStep(
            step_number=7,
            name="Discharge to 50% SOC",
            step_type=StepType.CC,
            target_value=-0.2,
            limit_value=0.50,
            limit_type="soc",
            description="Discharge to 50% SOC"
        ),
        
        TestStep(
            step_number=8,
            name="EIS at 50% SOC",
            step_type=StepType.EIS,
            target_value=3.7,
            metadata={
                "frequency_range": [0.01, 10000],
                "amplitude": 0.01,
                "points_per_decade": 10,
                "soc_target": 50
            },
            description="EIS measurement at 50% SOC"
        ),
        
        TestStep(
            step_number=9,
            name="Discharge to 25% SOC",
            step_type=StepType.CC,
            target_value=-0.2,
            limit_value=0.25,
            limit_type="soc",
            description="Discharge to 25% SOC"
        ),
        
        TestStep(
            step_number=10,
            name="EIS at 25% SOC",
            step_type=StepType.EIS,
            target_value=3.5,
            metadata={
                "frequency_range": [0.01, 10000],
                "amplitude": 0.01,
                "points_per_decade": 10,
                "soc_target": 25
            },
            description="EIS measurement at 25% SOC"
        )
    ]
    
    safety_limits = SafetyLimits(
        max_voltage=4.3,
        min_voltage=2.8,
        max_current=5.0,
        max_temperature=40.0,
        min_temperature=15.0,
    )
    
    return TestProfile(
        name="EIS Characterization Profile",
        description="Electrochemical Impedance Spectroscopy at multiple SOC levels",
        profile_type=TestProfileType.EIS,
        steps=steps,
        default_cycles=1,
        safety_limits=safety_limits,
        category="Characterization",
        tags=["eis", "impedance", "characterization", "soc"],
        metadata={
            "test_type": "eis_soc_sweep",
            "frequency_range": [0.01, 10000],
            "soc_points": [100, 75, 50, 25],
            "estimated_duration_hours": 8
        }
    )


def create_pulse_power_profile() -> TestProfile:
    """Create a pulse power characterization profile."""
    
    steps = []
    step_num = 1
    
    # Initial charge to 100% SOC
    steps.append(TestStep(
        step_number=step_num,
        name="Full Charge",
        step_type=StepType.CC,
        target_value=1.0,
        limit_value=4.2,
        limit_type="voltage",
        description="Charge to 100% SOC"
    ))
    step_num += 1
    
    steps.append(TestStep(
        step_number=step_num,
        name="CV Top-off",
        step_type=StepType.CV,
        target_value=4.2,
        limit_value=0.05,
        limit_type="current",
        description="CV top-off charge"
    ))
    step_num += 1
    
    # Pulse testing at different SOC levels
    soc_levels = [100, 90, 80, 70, 60, 50, 40, 30, 20, 10]
    pulse_currents = [1, 2, 5, 10, 15, 20]  # Different C-rates
    
    for soc in soc_levels:
        # Rest at SOC level
        steps.append(TestStep(
            step_number=step_num,
            name=f"Rest at {soc}% SOC",
            step_type=StepType.REST,
            duration=600,  # 10 minutes
            description=f"Rest at {soc}% SOC before pulse testing"
        ))
        step_num += 1
        
        # Discharge pulses at different C-rates
        for current in pulse_currents:
            steps.append(TestStep(
                step_number=step_num,
                name=f"Discharge Pulse {current}A at {soc}% SOC",
                step_type=StepType.PULSE,
                target_value=-current,
                duration=10,  # 10 second pulse
                metadata={
                    "pulse_type": "discharge",
                    "pulse_duration": 10,
                    "rest_duration": 30,
                    "soc_level": soc,
                    "c_rate": current
                },
                description=f"{current}A discharge pulse for 10s at {soc}% SOC"
            ))
            step_num += 1
            
            # Rest between pulses
            steps.append(TestStep(
                step_number=step_num,
                name=f"Rest after {current}A pulse",
                step_type=StepType.REST,
                duration=30,  # 30 seconds
                description="Rest between pulses"
            ))
            step_num += 1
        
        # Charge pulses
        for current in pulse_currents:
            steps.append(TestStep(
                step_number=step_num,
                name=f"Charge Pulse {current}A at {soc}% SOC",
                step_type=StepType.PULSE,
                target_value=current,
                duration=10,
                metadata={
                    "pulse_type": "charge",
                    "pulse_duration": 10,
                    "rest_duration": 30,
                    "soc_level": soc,
                    "c_rate": current
                },
                description=f"{current}A charge pulse for 10s at {soc}% SOC"
            ))
            step_num += 1
            
            steps.append(TestStep(
                step_number=step_num,
                name=f"Rest after {current}A charge pulse",
                step_type=StepType.REST,
                duration=30,
                description="Rest between pulses"
            ))
            step_num += 1
        
        # Discharge to next SOC level (except for last level)
        if soc > 10:
            steps.append(TestStep(
                step_number=step_num,
                name=f"Discharge to {soc-10}% SOC",
                step_type=StepType.CC,
                target_value=-0.5,  # C/2 discharge
                limit_value=(soc-10)/100,
                limit_type="soc",
                description=f"Discharge to {soc-10}% SOC"
            ))
            step_num += 1
    
    safety_limits = SafetyLimits(
        max_voltage=4.3,
        min_voltage=2.5,
        max_current=25.0,  # Higher current for pulse testing
        max_temperature=50.0,
        min_temperature=10.0,
    )
    
    return TestProfile(
        name="Pulse Power Characterization",
        description="Pulse power testing at multiple SOC levels and C-rates",
        profile_type=TestProfileType.PULSE,
        steps=steps,
        default_cycles=1,
        safety_limits=safety_limits,
        category="Characterization",
        tags=["pulse", "power", "characterization", "c-rate"],
        metadata={
            "test_type": "pulse_power",
            "soc_levels": soc_levels,
            "pulse_currents": pulse_currents,
            "pulse_duration": 10,
            "rest_duration": 30,
            "estimated_duration_hours": 12
        }
    )


def create_drive_cycle_profile() -> TestProfile:
    """Create a drive cycle replay profile."""
    
    # This would typically load drive cycle data from a file
    # For this example, we'll create a simplified WLTP-like cycle
    
    steps = [
        # Pre-conditioning
        TestStep(
            step_number=1,
            name="Pre-condition Charge",
            step_type=StepType.CC,
            target_value=1.0,
            limit_value=4.2,
            limit_type="voltage",
            description="Charge to 100% SOC"
        ),
        
        TestStep(
            step_number=2,
            name="CV Top-off",
            step_type=StepType.CV,
            target_value=4.2,
            limit_value=0.05,
            limit_type="current",
            description="CV top-off"
        ),
        
        TestStep(
            step_number=3,
            name="Pre-cycle Rest",
            step_type=StepType.REST,
            duration=3600,  # 1 hour
            description="Rest before drive cycle"
        ),
        
        # Drive cycle segments
        TestStep(
            step_number=4,
            name="WLTP Low Speed Phase",
            step_type=StepType.CUSTOM,
            metadata={
                "cycle_type": "wltp_low",
                "duration": 589,  # seconds
                "max_power": 15000,  # 15kW
                "avg_power": 3000,   # 3kW
                "profile_file": "wltp_low_speed.csv"
            },
            description="WLTP low speed phase (0-589s)"
        ),
        
        TestStep(
            step_number=5,
            name="WLTP Medium Speed Phase",
            step_type=StepType.CUSTOM,
            metadata={
                "cycle_type": "wltp_medium",
                "duration": 433,
                "max_power": 25000,  # 25kW
                "avg_power": 8000,   # 8kW
                "profile_file": "wltp_medium_speed.csv"
            },
            description="WLTP medium speed phase (589-1022s)"
        ),
        
        TestStep(
            step_number=6,
            name="WLTP High Speed Phase",
            step_type=StepType.CUSTOM,
            metadata={
                "cycle_type": "wltp_high",
                "duration": 455,
                "max_power": 40000,  # 40kW
                "avg_power": 12000,  # 12kW
                "profile_file": "wltp_high_speed.csv"
            },
            description="WLTP high speed phase (1022-1477s)"
        ),
        
        TestStep(
            step_number=7,
            name="WLTP Extra High Speed Phase",
            step_type=StepType.CUSTOM,
            metadata={
                "cycle_type": "wltp_extra_high",
                "duration": 323,
                "max_power": 60000,  # 60kW
                "avg_power": 18000,  # 18kW
                "profile_file": "wltp_extra_high_speed.csv"
            },
            description="WLTP extra high speed phase (1477-1800s)"
        ),
        
        # Post-cycle analysis
        TestStep(
            step_number=8,
            name="Post-cycle Rest",
            step_type=StepType.REST,
            duration=1800,  # 30 minutes
            description="Rest after drive cycle"
        ),
        
        TestStep(
            step_number=9,
            name="Capacity Check Discharge",
            step_type=StepType.CC,
            target_value=-1.0,  # 1C discharge
            limit_value=2.5,
            limit_type="voltage",
            description="Capacity check discharge"
        )
    ]
    
    safety_limits = SafetyLimits(
        max_voltage=4.3,
        min_voltage=2.5,
        max_current=200.0,  # High current for drive cycle
        max_power=70000,    # 70kW max power
        max_temperature=60.0,
        min_temperature=0.0,
    )
    
    return TestProfile(
        name="WLTP Drive Cycle",
        description="Worldwide Harmonized Light Vehicles Test Procedure drive cycle",
        profile_type=TestProfileType.DRIVE_CYCLE,
        steps=steps,
        default_cycles=5,  # Multiple cycles for aging
        safety_limits=safety_limits,
        category="Drive Cycles",
        tags=["wltp", "drive-cycle", "automotive", "ev"],
        metadata={
            "cycle_type": "wltp",
            "total_duration": 1800,  # 30 minutes
            "total_distance": 23.25,  # km
            "max_speed": 131.3,  # km/h
            "avg_speed": 46.5,   # km/h
            "estimated_energy": 5.5  # kWh
        }
    )


def create_calendar_aging_profile() -> TestProfile:
    """Create a calendar aging test profile."""
    
    steps = [
        # Initial characterization
        TestStep(
            step_number=1,
            name="Initial Full Charge",
            step_type=StepType.CC,
            target_value=0.5,  # C/2 charge
            limit_value=4.2,
            limit_type="voltage",
            description="Initial charge for characterization"
        ),
        
        TestStep(
            step_number=2,
            name="Initial CV Charge",
            step_type=StepType.CV,
            target_value=4.2,
            limit_value=0.05,
            limit_type="current",
            description="CV phase for full charge"
        ),
        
        TestStep(
            step_number=3,
            name="Initial Capacity Test",
            step_type=StepType.CC,
            target_value=-0.5,  # C/2 discharge
            limit_value=2.5,
            limit_type="voltage",
            description="Initial capacity measurement"
        ),
        
        # Set SOC for aging
        TestStep(
            step_number=4,
            name="Charge to Aging SOC",
            step_type=StepType.CC,
            target_value=0.5,
            limit_value=0.60,  # 60% SOC for aging
            limit_type="soc",
            description="Charge to 60% SOC for calendar aging"
        ),
        
        # Calendar aging storage
        TestStep(
            step_number=5,
            name="Calendar Aging Storage",
            step_type=StepType.REST,
            duration=86400 * 30,  # 30 days
            metadata={
                "aging_type": "calendar",
                "storage_soc": 60,
                "storage_temperature": 45,  # °C
                "check_interval": 86400,  # Daily checks
                "voltage_float": True,
                "float_voltage": 3.8
            },
            description="30-day calendar aging at 60% SOC and 45°C"
        ),
        
        # Periodic capacity checks during aging
        TestStep(
            step_number=6,
            name="Weekly Capacity Check",
            step_type=StepType.CUSTOM,
            metadata={
                "check_type": "capacity",
                "frequency": "weekly",
                "full_cycle": True,
                "return_to_aging_soc": True
            },
            description="Weekly capacity check during aging"
        ),
        
        # Final characterization
        TestStep(
            step_number=7,
            name="Final Full Charge",
            step_type=StepType.CC,
            target_value=0.5,
            limit_value=4.2,
            limit_type="voltage",
            description="Final charge for characterization"
        ),
        
        TestStep(
            step_number=8,
            name="Final CV Charge",
            step_type=StepType.CV,
            target_value=4.2,
            limit_value=0.05,
            limit_type="current",
            description="Final CV phase"
        ),
        
        TestStep(
            step_number=9,
            name="Final Capacity Test",
            step_type=StepType.CC,
            target_value=-0.5,
            limit_value=2.5,
            limit_type="voltage",
            description="Final capacity measurement"
        )
    ]
    
    safety_limits = SafetyLimits(
        max_voltage=4.3,
        min_voltage=2.5,
        max_current=5.0,
        max_temperature=50.0,  # Higher for aging
        min_temperature=40.0,  # Controlled temperature
    )
    
    return TestProfile(
        name="Calendar Aging Test",
        description="30-day calendar aging test at 60% SOC and 45°C",
        profile_type=TestProfileType.CALENDAR_AGING,
        steps=steps,
        default_cycles=1,
        safety_limits=safety_limits,
        category="Aging",
        tags=["calendar", "aging", "storage", "temperature"],
        metadata={
            "aging_duration_days": 30,
            "storage_soc": 60,
            "storage_temperature": 45,
            "check_frequency": "weekly",
            "test_type": "calendar_aging"
        }
    )


async def main():
    """Demonstrate creating various advanced test profiles."""
    
    print("Advanced Test Profile Examples")
    print("=" * 50)
    
    # Create different types of profiles
    profiles = [
        create_eis_profile(),
        create_pulse_power_profile(),
        create_drive_cycle_profile(),
        create_calendar_aging_profile()
    ]
    
    for profile in profiles:
        print(f"\nProfile: {profile.name}")
        print(f"Type: {profile.profile_type.value}")
        print(f"Steps: {len(profile.steps)}")
        print(f"Category: {profile.category}")
        print(f"Tags: {', '.join(profile.tags)}")
        print(f"Description: {profile.description}")
        
        if profile.estimated_duration:
            hours = profile.estimated_duration / 3600
            print(f"Estimated Duration: {hours:.1f} hours")
        
        # Validate profile
        issues = profile.validate()
        if issues:
            print(f"Validation Issues: {', '.join(issues)}")
        else:
            print("Validation: PASSED")
        
        print("-" * 30)
    
    print(f"\nCreated {len(profiles)} advanced test profiles")
    print("These profiles demonstrate the flexibility of the BTS system")
    print("for various battery testing applications.")


if __name__ == "__main__":
    asyncio.run(main())
