"""Configuration settings management."""

import os
import toml
from pathlib import Path
from typing import Dict, Any, Optional, List
from pydantic import BaseSettings, Field


class DatabaseSettings(BaseSettings):
    """Database configuration settings."""
    
    primary: str = "sqlite:///data/bts.db"
    timeseries: str = "sqlite:///data/bts_timeseries.db"
    max_connections: int = 20
    min_connections: int = 5
    connection_timeout: int = 30
    retry_attempts: int = 3
    
    class Config:
        env_prefix = "BTS_DB_"


class SafetySettings(BaseSettings):
    """Safety configuration settings."""
    
    global_voltage_limit: float = 5.0
    global_current_limit: float = 100.0
    temperature_limit: float = 60.0
    pressure_limit: float = 2.0
    emergency_stop_enabled: bool = True
    safety_check_interval: float = 1.0
    fire_alarm_enabled: bool = False
    fire_alarm_protocol: str = "modbus_tcp"
    fire_alarm_address: str = "*************:502"
    
    class Config:
        env_prefix = "BTS_SAFETY_"


class HardwareSettings(BaseSettings):
    """Hardware configuration settings."""
    
    auto_discovery: bool = True
    discovery_interval: int = 30
    connection_timeout: int = 30
    retry_attempts: int = 3
    hot_plug_detection: bool = True
    
    # Modbus settings
    modbus_timeout: float = 5.0
    modbus_retries: int = 3
    modbus_unit_id: int = 1
    
    # CANbus settings
    canbus_interface: str = "socketcan"
    canbus_channel: str = "can0"
    canbus_bitrate: int = 500000
    
    # Serial settings
    serial_baudrate: int = 9600
    serial_timeout: float = 1.0
    serial_parity: str = "N"
    serial_stopbits: int = 1
    serial_bytesize: int = 8
    
    class Config:
        env_prefix = "BTS_HW_"


class GUISettings(BaseSettings):
    """GUI configuration settings."""
    
    theme: str = "dark"
    language: str = "en"
    auto_save_interval: int = 300
    plot_buffer_size: int = 10000
    update_interval: int = 100
    default_colors: List[str] = [
        "#1f77b4", "#ff7f0e", "#2ca02c", "#d62728", "#9467bd"
    ]
    line_width: int = 2
    grid_enabled: bool = True
    legend_enabled: bool = True
    
    class Config:
        env_prefix = "BTS_GUI_"


class WebSettings(BaseSettings):
    """Web interface configuration settings."""
    
    enabled: bool = False
    host: str = "0.0.0.0"
    port: int = 8000
    cors_origins: List[str] = ["http://localhost:3000"]
    api_prefix: str = "/api/v1"
    
    class Config:
        env_prefix = "BTS_WEB_"


class AuthSettings(BaseSettings):
    """Authentication configuration settings."""
    
    enabled: bool = True
    session_timeout: int = 3600
    password_min_length: int = 8
    require_password_complexity: bool = True
    max_login_attempts: int = 5
    lockout_duration: int = 300
    jwt_secret_key: str = "your-secret-key-change-this"
    jwt_algorithm: str = "HS256"
    jwt_access_token_expire_minutes: int = 30
    
    class Config:
        env_prefix = "BTS_AUTH_"


class LoggingSettings(BaseSettings):
    """Logging configuration settings."""
    
    level: str = "INFO"
    format: str = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
    file_enabled: bool = True
    file_path: str = "logs/bts.log"
    file_max_size: str = "10MB"
    file_backup_count: int = 5
    console_enabled: bool = True
    
    class Config:
        env_prefix = "BTS_LOG_"


class DataExportSettings(BaseSettings):
    """Data export configuration settings."""
    
    default_format: str = "csv"
    include_metadata: bool = True
    timestamp_format: str = "ISO8601"
    decimal_places: int = 6
    csv_delimiter: str = ","
    csv_quote_char: str = '"'
    csv_line_terminator: str = "\n"
    
    class Config:
        env_prefix = "BTS_EXPORT_"


class PerformanceSettings(BaseSettings):
    """Performance tuning settings."""
    
    max_worker_threads: int = 4
    data_buffer_size: int = 1000
    batch_write_size: int = 100
    gc_threshold: int = 1000
    
    class Config:
        env_prefix = "BTS_PERF_"


class Settings(BaseSettings):
    """Main configuration settings for the Battery Testing System."""
    
    # System settings
    name: str = "Battery Testing System"
    version: str = "0.1.0"
    data_retention_days: int = 365
    max_channels: int = 1000
    sampling_rate_hz: float = 1.0
    log_level: str = "INFO"
    debug_mode: bool = False
    
    # Sub-configurations
    database: DatabaseSettings = Field(default_factory=DatabaseSettings)
    safety: SafetySettings = Field(default_factory=SafetySettings)
    hardware: HardwareSettings = Field(default_factory=HardwareSettings)
    gui: GUISettings = Field(default_factory=GUISettings)
    web: WebSettings = Field(default_factory=WebSettings)
    auth: AuthSettings = Field(default_factory=AuthSettings)
    logging: LoggingSettings = Field(default_factory=LoggingSettings)
    data_export: DataExportSettings = Field(default_factory=DataExportSettings)
    performance: PerformanceSettings = Field(default_factory=PerformanceSettings)
    
    class Config:
        env_prefix = "BTS_"
        case_sensitive = False
    
    @classmethod
    def load_from_file(cls, config_path: Optional[str] = None) -> "Settings":
        """Load settings from a TOML configuration file."""
        if config_path is None:
            # Look for config files in standard locations
            possible_paths = [
                "config/default.toml",
                "bts.toml",
                os.path.expanduser("~/.bts/config.toml"),
                "/etc/bts/config.toml",
            ]
            
            for path in possible_paths:
                if os.path.exists(path):
                    config_path = path
                    break
        
        if config_path and os.path.exists(config_path):
            try:
                with open(config_path, 'r') as f:
                    config_data = toml.load(f)
                
                # Create settings with loaded data
                return cls(**config_data)
                
            except Exception as e:
                print(f"Warning: Failed to load config from {config_path}: {e}")
                print("Using default settings")
        
        # Return default settings if no config file found
        return cls()
    
    def save_to_file(self, config_path: str) -> bool:
        """Save current settings to a TOML file."""
        try:
            # Ensure directory exists
            Path(config_path).parent.mkdir(parents=True, exist_ok=True)
            
            # Convert to dict and save
            config_data = self.dict()
            with open(config_path, 'w') as f:
                toml.dump(config_data, f)
            
            return True
            
        except Exception as e:
            print(f"Failed to save config to {config_path}: {e}")
            return False
    
    def get_database_url(self, db_type: str = "primary") -> str:
        """Get database URL for specified type."""
        if db_type == "primary":
            return self.database.primary
        elif db_type == "timeseries":
            return self.database.timeseries
        else:
            raise ValueError(f"Unknown database type: {db_type}")
    
    def is_development_mode(self) -> bool:
        """Check if running in development mode."""
        return self.debug_mode or self.log_level.upper() == "DEBUG"
    
    def get_log_config(self) -> Dict[str, Any]:
        """Get logging configuration dictionary."""
        return {
            "version": 1,
            "disable_existing_loggers": False,
            "formatters": {
                "default": {
                    "format": self.logging.format,
                },
            },
            "handlers": {
                "console": {
                    "class": "logging.StreamHandler",
                    "formatter": "default",
                    "level": self.logging.level,
                } if self.logging.console_enabled else None,
                "file": {
                    "class": "logging.handlers.RotatingFileHandler",
                    "formatter": "default",
                    "filename": self.logging.file_path,
                    "maxBytes": self._parse_size(self.logging.file_max_size),
                    "backupCount": self.logging.file_backup_count,
                    "level": self.logging.level,
                } if self.logging.file_enabled else None,
            },
            "root": {
                "level": self.logging.level,
                "handlers": [
                    name for name, handler in {
                        "console": self.logging.console_enabled,
                        "file": self.logging.file_enabled,
                    }.items() if handler
                ],
            },
        }
    
    def _parse_size(self, size_str: str) -> int:
        """Parse size string like '10MB' to bytes."""
        size_str = size_str.upper()
        if size_str.endswith('KB'):
            return int(size_str[:-2]) * 1024
        elif size_str.endswith('MB'):
            return int(size_str[:-2]) * 1024 * 1024
        elif size_str.endswith('GB'):
            return int(size_str[:-2]) * 1024 * 1024 * 1024
        else:
            return int(size_str)


# Global settings instance
settings = Settings.load_from_file()
