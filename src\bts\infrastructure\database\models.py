"""SQLAlchemy database models."""

from datetime import datetime
from typing import Optional

try:
    from sqlalchemy import Column, String, Integer, Float, DateTime, <PERSON>olean, Text, JSON, ForeignKey
    from sqlalchemy.ext.declarative import declarative_base
    from sqlalchemy.orm import relationship
    from sqlalchemy.dialects.postgresql import UUID
    from sqlalchemy import String as SQLString
    SQLALCHEMY_AVAILABLE = True
except ImportError:
    SQLALCHEMY_AVAILABLE = False

if SQLALCHEMY_AVAILABLE:
    Base = declarative_base()
    
    class TestModel(Base):
        """Database model for tests."""
        __tablename__ = "tests"
        
        id = Column(String(36), primary_key=True)
        name = Column(String(255), nullable=False)
        description = Column(Text)
        profile_id = Column(String(36))
        channel_id = Column(String(255))
        state = Column(String(50), nullable=False)
        
        created_at = Column(DateTime, default=datetime.utcnow)
        started_at = Column(DateTime)
        completed_at = Column(DateTime)
        updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
        
        current_step = Column(Integer)
        current_cycle = Column(Integer, default=1)
        total_cycles = Column(Integer, default=1)
        step_start_time = Column(DateTime)
        
        total_capacity = Column(Float)
        total_energy = Column(Float)
        peak_power = Column(Float)
        average_efficiency = Column(Float)
        
        error_message = Column(Text)
        warning_messages = Column(JSON)
        
        tags = Column(JSON)
        metadata = Column(JSON)
        
        # Relationships
        measurements = relationship("MeasurementModel", back_populates="test")
    
    class MeasurementModel(Base):
        """Database model for measurements."""
        __tablename__ = "measurements"
        
        id = Column(String(36), primary_key=True)
        timestamp = Column(DateTime, nullable=False, index=True)
        channel_id = Column(String(255), nullable=False, index=True)
        test_id = Column(String(36), ForeignKey("tests.id"), index=True)
        
        # Primary measurements
        voltage = Column(Float)
        current = Column(Float)
        temperature = Column(Float)
        
        # Derived measurements
        capacity = Column(Float)
        energy = Column(Float)
        power = Column(Float)
        resistance = Column(Float)
        
        # State measurements
        soc = Column(Float)
        soh = Column(Float)
        
        # Environmental measurements
        pressure = Column(Float)
        humidity = Column(Float)
        
        # Test context
        step_number = Column(Integer)
        step_time = Column(Float)
        cycle_number = Column(Integer)
        
        metadata = Column(JSON)
        
        # Relationships
        test = relationship("TestModel", back_populates="measurements")
    
    class ChannelModel(Base):
        """Database model for channels."""
        __tablename__ = "channels"
        
        id = Column(String(255), primary_key=True)
        name = Column(String(255), nullable=False)
        instrument_id = Column(String(255), nullable=False)
        
        state = Column(String(50), nullable=False)
        created_at = Column(DateTime, default=datetime.utcnow)
        updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
        
        current_test_id = Column(String(36))
        current_step = Column(Integer)
        step_start_time = Column(DateTime)
        
        hardware_channel = Column(Integer)
        calibration_data = Column(JSON)
        
        error_message = Column(Text)
        warning_message = Column(Text)
        
        description = Column(Text)
        tags = Column(JSON)
        metadata = Column(JSON)
    
    class InstrumentModel(Base):
        """Database model for instruments."""
        __tablename__ = "instruments"
        
        id = Column(String(255), primary_key=True)
        name = Column(String(255), nullable=False)
        instrument_type = Column(String(50), nullable=False)
        manufacturer = Column(String(255))
        model = Column(String(255))
        serial_number = Column(String(255))
        
        connection_string = Column(String(500))
        driver_name = Column(String(100))
        
        state = Column(String(50), nullable=False)
        created_at = Column(DateTime, default=datetime.utcnow)
        updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
        last_connected = Column(DateTime)
        
        max_channels = Column(Integer, default=1)
        max_voltage = Column(Float)
        max_current = Column(Float)
        max_power = Column(Float)
        voltage_resolution = Column(Float)
        current_resolution = Column(Float)
        
        configuration = Column(JSON)
        calibration_data = Column(JSON)
        
        firmware_version = Column(String(100))
        error_message = Column(Text)
        warning_message = Column(Text)
        
        description = Column(Text)
        location = Column(String(255))
        tags = Column(JSON)
        metadata = Column(JSON)
    
    class TestProfileModel(Base):
        """Database model for test profiles."""
        __tablename__ = "test_profiles"
        
        id = Column(String(36), primary_key=True)
        name = Column(String(255), nullable=False)
        description = Column(Text)
        profile_type = Column(String(50), nullable=False)
        
        steps = Column(JSON)  # Serialized test steps
        default_cycles = Column(Integer, default=1)
        
        version = Column(String(50), default="1.0")
        author = Column(String(255))
        created_at = Column(DateTime, default=datetime.utcnow)
        updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
        
        tags = Column(JSON)
        category = Column(String(100))
        
        usage_count = Column(Integer, default=0)
        last_used = Column(DateTime)
        
        min_voltage = Column(Float)
        max_voltage = Column(Float)
        min_capacity = Column(Float)
        max_capacity = Column(Float)
        
        metadata = Column(JSON)
    
    class UserModel(Base):
        """Database model for users."""
        __tablename__ = "users"
        
        id = Column(String(36), primary_key=True)
        username = Column(String(100), unique=True, nullable=False)
        email = Column(String(255), unique=True)
        full_name = Column(String(255))
        
        password_hash = Column(String(255))
        is_active = Column(Boolean, default=True)
        is_verified = Column(Boolean, default=False)
        
        role = Column(String(50), nullable=False)
        permissions = Column(JSON)
        
        created_at = Column(DateTime, default=datetime.utcnow)
        updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
        last_login = Column(DateTime)
        
        failed_login_attempts = Column(Integer, default=0)
        locked_until = Column(DateTime)
        
        preferences = Column(JSON)
        metadata = Column(JSON)
    
    class AuditLogModel(Base):
        """Database model for audit logs."""
        __tablename__ = "audit_logs"
        
        id = Column(Integer, primary_key=True, autoincrement=True)
        timestamp = Column(DateTime, default=datetime.utcnow, index=True)
        user_id = Column(String(36))
        action = Column(String(100), nullable=False)
        resource_type = Column(String(50))
        resource_id = Column(String(255))
        details = Column(JSON)
        ip_address = Column(String(45))
        user_agent = Column(Text)
    
    class SafetyEventModel(Base):
        """Database model for safety events."""
        __tablename__ = "safety_events"
        
        id = Column(Integer, primary_key=True, autoincrement=True)
        timestamp = Column(DateTime, default=datetime.utcnow, index=True)
        level = Column(String(20), nullable=False)
        message = Column(Text, nullable=False)
        channel_id = Column(String(255))
        test_id = Column(String(36))
        measurement_id = Column(String(36))
        acknowledged = Column(Boolean, default=False)
        acknowledged_by = Column(String(36))
        acknowledged_at = Column(DateTime)
        metadata = Column(JSON)

else:
    # Fallback if SQLAlchemy is not available
    class Base:
        pass
    
    TestModel = None
    MeasurementModel = None
    ChannelModel = None
    InstrumentModel = None
    TestProfileModel = None
    UserModel = None
    AuditLogModel = None
    SafetyEventModel = None
