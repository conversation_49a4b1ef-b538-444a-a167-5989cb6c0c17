"""Dashboard widget for system overview."""

import logging
from typing import Optional, List

try:
    from PySide6.QtWidgets import (
        QWidget, QVBoxLayout, QHBoxLayout, QGridLayout,
        QLabel, QFrame, QPushButton, QProgressBar,
        QGroupBox, QScrollArea, QSizePolicy
    )
    from PySide6.QtCore import Qt, QTimer, Signal
    from PySide6.QtGui import QFont, QPalette
    PYSIDE6_AVAILABLE = True
except ImportError:
    PYSIDE6_AVAILABLE = False

if PYSIDE6_AVAILABLE:
    class StatusCard(QFrame):
        """A card widget for displaying status information."""
        
        def __init__(self, title: str, value: str = "", color: str = "blue", parent=None):
            super().__init__(parent)
            self.setFrameStyle(QFrame.Box)
            self.setLineWidth(1)
            self.setMinimumSize(200, 100)
            
            layout = QVBoxLayout(self)
            
            # Title
            title_label = QLabel(title)
            title_font = QFont()
            title_font.setBold(True)
            title_font.setPointSize(10)
            title_label.setFont(title_font)
            title_label.setAlignment(Qt.AlignCenter)
            layout.addWidget(title_label)
            
            # Value
            self.value_label = QLabel(value)
            value_font = QFont()
            value_font.setPointSize(16)
            value_font.setBold(True)
            self.value_label.setFont(value_font)
            self.value_label.setAlignment(Qt.AlignCenter)
            layout.addWidget(self.value_label)
            
            # Apply color styling
            self.set_color(color)
        
        def set_value(self, value: str):
            """Update the value displayed on the card."""
            self.value_label.setText(value)
        
        def set_color(self, color: str):
            """Set the color theme of the card."""
            color_map = {
                "blue": "#3498db",
                "green": "#2ecc71", 
                "red": "#e74c3c",
                "orange": "#f39c12",
                "purple": "#9b59b6",
                "gray": "#95a5a6"
            }
            
            color_code = color_map.get(color, "#3498db")
            self.setStyleSheet(f"""
                QFrame {{
                    border: 2px solid {color_code};
                    border-radius: 8px;
                    background-color: rgba({int(color_code[1:3], 16)}, {int(color_code[3:5], 16)}, {int(color_code[5:7], 16)}, 0.1);
                }}
                QLabel {{
                    border: none;
                    background-color: transparent;
                }}
            """)


    class DashboardWidget(QWidget):
        """Main dashboard widget showing system overview."""
        
        # Signals
        refresh_requested = Signal()
        
        def __init__(self, hardware_manager=None, test_engine=None, safety_manager=None, parent=None):
            super().__init__(parent)
            
            self.logger = logging.getLogger(__name__)
            
            # Store service references
            self.hardware_manager = hardware_manager
            self.test_engine = test_engine
            self.safety_manager = safety_manager
            
            # UI components
            self.status_cards = {}
            self.active_tests_area = None
            self.recent_events_area = None
            
            # Update timer
            self.update_timer = QTimer()
            self.update_timer.timeout.connect(self.update_dashboard)
            
            # Setup UI
            self.setup_ui()
            self.start_updates()
            
            self.logger.info("Dashboard widget initialized")
        
        def setup_ui(self):
            """Setup the dashboard user interface."""
            layout = QVBoxLayout(self)
            
            # Title
            title_label = QLabel("System Dashboard")
            title_font = QFont()
            title_font.setPointSize(18)
            title_font.setBold(True)
            title_label.setFont(title_font)
            title_label.setAlignment(Qt.AlignCenter)
            layout.addWidget(title_label)
            
            # Status cards section
            self.create_status_cards(layout)
            
            # Main content area
            content_layout = QHBoxLayout()
            layout.addLayout(content_layout)
            
            # Active tests section
            self.create_active_tests_section(content_layout)
            
            # Recent events section
            self.create_recent_events_section(content_layout)
            
            # Control buttons
            self.create_control_buttons(layout)
        
        def create_status_cards(self, parent_layout):
            """Create status cards showing key metrics."""
            cards_group = QGroupBox("System Status")
            cards_layout = QGridLayout(cards_group)
            
            # Create status cards
            self.status_cards["instruments"] = StatusCard("Connected Instruments", "0/0", "blue")
            self.status_cards["channels"] = StatusCard("Available Channels", "0", "green")
            self.status_cards["active_tests"] = StatusCard("Active Tests", "0", "orange")
            self.status_cards["safety"] = StatusCard("Safety Status", "Unknown", "gray")
            
            # Arrange cards in grid
            cards_layout.addWidget(self.status_cards["instruments"], 0, 0)
            cards_layout.addWidget(self.status_cards["channels"], 0, 1)
            cards_layout.addWidget(self.status_cards["active_tests"], 1, 0)
            cards_layout.addWidget(self.status_cards["safety"], 1, 1)
            
            parent_layout.addWidget(cards_group)
        
        def create_active_tests_section(self, parent_layout):
            """Create section showing active tests."""
            tests_group = QGroupBox("Active Tests")
            tests_layout = QVBoxLayout(tests_group)
            
            # Scroll area for tests
            scroll_area = QScrollArea()
            scroll_area.setWidgetResizable(True)
            scroll_area.setMinimumHeight(200)
            
            self.active_tests_area = QWidget()
            self.active_tests_layout = QVBoxLayout(self.active_tests_area)
            
            # Placeholder
            placeholder = QLabel("No active tests")
            placeholder.setAlignment(Qt.AlignCenter)
            placeholder.setStyleSheet("color: gray; font-style: italic;")
            self.active_tests_layout.addWidget(placeholder)
            
            scroll_area.setWidget(self.active_tests_area)
            tests_layout.addWidget(scroll_area)
            
            parent_layout.addWidget(tests_group)
        
        def create_recent_events_section(self, parent_layout):
            """Create section showing recent events."""
            events_group = QGroupBox("Recent Events")
            events_layout = QVBoxLayout(events_group)
            
            # Scroll area for events
            scroll_area = QScrollArea()
            scroll_area.setWidgetResizable(True)
            scroll_area.setMinimumHeight(200)
            
            self.recent_events_area = QWidget()
            self.recent_events_layout = QVBoxLayout(self.recent_events_area)
            
            # Placeholder
            placeholder = QLabel("No recent events")
            placeholder.setAlignment(Qt.AlignCenter)
            placeholder.setStyleSheet("color: gray; font-style: italic;")
            self.recent_events_layout.addWidget(placeholder)
            
            scroll_area.setWidget(self.recent_events_area)
            events_layout.addWidget(scroll_area)
            
            parent_layout.addWidget(events_group)
        
        def create_control_buttons(self, parent_layout):
            """Create control buttons."""
            buttons_layout = QHBoxLayout()
            
            # Refresh button
            refresh_btn = QPushButton("🔄 Refresh")
            refresh_btn.clicked.connect(self.refresh_dashboard)
            buttons_layout.addWidget(refresh_btn)
            
            # Emergency stop button
            emergency_btn = QPushButton("🛑 Emergency Stop")
            emergency_btn.setStyleSheet("""
                QPushButton {
                    background-color: #e74c3c;
                    color: white;
                    font-weight: bold;
                    padding: 10px;
                    border-radius: 5px;
                }
                QPushButton:hover {
                    background-color: #c0392b;
                }
            """)
            emergency_btn.clicked.connect(self.emergency_stop)
            buttons_layout.addWidget(emergency_btn)
            
            buttons_layout.addStretch()
            parent_layout.addLayout(buttons_layout)
        
        def start_updates(self):
            """Start periodic dashboard updates."""
            self.update_timer.start(2000)  # Update every 2 seconds
            self.update_dashboard()  # Initial update
        
        def update_dashboard(self):
            """Update dashboard with current data."""
            try:
                self.update_status_cards()
                self.update_active_tests()
                self.update_recent_events()
            except Exception as e:
                self.logger.error(f"Error updating dashboard: {e}")
        
        def update_status_cards(self):
            """Update the status cards with current data."""
            try:
                if self.hardware_manager:
                    # Instruments
                    connected = len(self.hardware_manager.get_connected_instruments())
                    total = len(self.hardware_manager.get_all_instruments())
                    self.status_cards["instruments"].set_value(f"{connected}/{total}")
                    
                    if connected > 0:
                        self.status_cards["instruments"].set_color("green")
                    else:
                        self.status_cards["instruments"].set_color("red")
                    
                    # Channels
                    available = len(self.hardware_manager.get_available_channels())
                    self.status_cards["channels"].set_value(str(available))
                    
                    if available > 0:
                        self.status_cards["channels"].set_color("green")
                    else:
                        self.status_cards["channels"].set_color("orange")
                
                if self.test_engine:
                    # Active tests
                    active_count = self.test_engine.active_test_count
                    self.status_cards["active_tests"].set_value(str(active_count))
                    
                    if active_count > 0:
                        self.status_cards["active_tests"].set_color("blue")
                    else:
                        self.status_cards["active_tests"].set_color("gray")
                
                if self.safety_manager:
                    # Safety status
                    if self.safety_manager.is_emergency_stop_active:
                        self.status_cards["safety"].set_value("EMERGENCY")
                        self.status_cards["safety"].set_color("red")
                    elif self.safety_manager.safety_enabled:
                        self.status_cards["safety"].set_value("ACTIVE")
                        self.status_cards["safety"].set_color("green")
                    else:
                        self.status_cards["safety"].set_value("DISABLED")
                        self.status_cards["safety"].set_color("orange")
                
            except Exception as e:
                self.logger.error(f"Error updating status cards: {e}")
        
        def update_active_tests(self):
            """Update the active tests display."""
            try:
                # Clear existing widgets
                for i in reversed(range(self.active_tests_layout.count())):
                    child = self.active_tests_layout.itemAt(i).widget()
                    if child:
                        child.setParent(None)
                
                if self.test_engine and self.test_engine.active_test_count > 0:
                    # Show active tests
                    active_tests = self.test_engine.active_tests
                    
                    for test in active_tests:
                        test_widget = self.create_test_widget(test)
                        self.active_tests_layout.addWidget(test_widget)
                else:
                    # Show placeholder
                    placeholder = QLabel("No active tests")
                    placeholder.setAlignment(Qt.AlignCenter)
                    placeholder.setStyleSheet("color: gray; font-style: italic;")
                    self.active_tests_layout.addWidget(placeholder)
                
            except Exception as e:
                self.logger.error(f"Error updating active tests: {e}")
        
        def create_test_widget(self, test):
            """Create a widget for displaying test information."""
            widget = QFrame()
            widget.setFrameStyle(QFrame.Box)
            widget.setLineWidth(1)
            
            layout = QVBoxLayout(widget)
            
            # Test name and status
            header_layout = QHBoxLayout()
            name_label = QLabel(test.name)
            name_label.setStyleSheet("font-weight: bold;")
            header_layout.addWidget(name_label)
            
            status_label = QLabel(test.state.value.upper())
            status_label.setStyleSheet("color: blue; font-weight: bold;")
            header_layout.addWidget(status_label)
            
            layout.addLayout(header_layout)
            
            # Progress bar
            if hasattr(test, 'progress_percentage'):
                progress = QProgressBar()
                progress.setValue(int(test.progress_percentage))
                progress.setFormat(f"{test.progress_percentage:.1f}%")
                layout.addWidget(progress)
            
            # Test details
            if hasattr(test, 'channel_id') and test.channel_id:
                channel_label = QLabel(f"Channel: {test.channel_id}")
                channel_label.setStyleSheet("color: gray; font-size: 10px;")
                layout.addWidget(channel_label)
            
            return widget
        
        def update_recent_events(self):
            """Update the recent events display."""
            try:
                # Clear existing widgets
                for i in reversed(range(self.recent_events_layout.count())):
                    child = self.recent_events_layout.itemAt(i).widget()
                    if child:
                        child.setParent(None)
                
                if self.safety_manager:
                    # Get recent safety events
                    recent_events = self.safety_manager.get_recent_events(5)
                    
                    if recent_events:
                        for event in recent_events:
                            event_widget = self.create_event_widget(event)
                            self.recent_events_layout.addWidget(event_widget)
                    else:
                        # Show placeholder
                        placeholder = QLabel("No recent events")
                        placeholder.setAlignment(Qt.AlignCenter)
                        placeholder.setStyleSheet("color: gray; font-style: italic;")
                        self.recent_events_layout.addWidget(placeholder)
                else:
                    # Show placeholder
                    placeholder = QLabel("No recent events")
                    placeholder.setAlignment(Qt.AlignCenter)
                    placeholder.setStyleSheet("color: gray; font-style: italic;")
                    self.recent_events_layout.addWidget(placeholder)
                
            except Exception as e:
                self.logger.error(f"Error updating recent events: {e}")
        
        def create_event_widget(self, event):
            """Create a widget for displaying event information."""
            widget = QFrame()
            widget.setFrameStyle(QFrame.Box)
            widget.setLineWidth(1)
            
            layout = QVBoxLayout(widget)
            
            # Event header
            header_layout = QHBoxLayout()
            
            # Level indicator
            level_colors = {
                "INFO": "blue",
                "WARNING": "orange", 
                "CRITICAL": "red",
                "EMERGENCY": "red"
            }
            
            level_label = QLabel(event.level.value.upper())
            color = level_colors.get(event.level.value.upper(), "gray")
            level_label.setStyleSheet(f"color: {color}; font-weight: bold;")
            header_layout.addWidget(level_label)
            
            # Timestamp
            time_label = QLabel(event.timestamp.strftime("%H:%M:%S"))
            time_label.setStyleSheet("color: gray; font-size: 10px;")
            header_layout.addWidget(time_label)
            
            layout.addLayout(header_layout)
            
            # Event message
            message_label = QLabel(event.message)
            message_label.setWordWrap(True)
            message_label.setStyleSheet("font-size: 11px;")
            layout.addWidget(message_label)
            
            return widget
        
        def refresh_dashboard(self):
            """Manually refresh the dashboard."""
            self.refresh_requested.emit()
            self.update_dashboard()
        
        def emergency_stop(self):
            """Trigger emergency stop."""
            if self.safety_manager:
                # This would trigger emergency stop
                self.logger.warning("Emergency stop requested from dashboard")
                # self.safety_manager.trigger_emergency_stop("User requested from dashboard")

else:
    # Fallback when PySide6 is not available
    class DashboardWidget:
        def __init__(self, *args, **kwargs):
            raise ImportError("PySide6 is required for GUI functionality")
