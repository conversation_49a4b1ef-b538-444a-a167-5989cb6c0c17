[build-system]
requires = ["setuptools>=61.0", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "battery-testing-system"
version = "0.1.0"
description = "Modular, extensible battery testing software for laboratories and manufacturing"
readme = "README.md"
license = {text = "MIT"}
authors = [
    {name = "Battery Testing System Team", email = "<EMAIL>"}
]
maintainers = [
    {name = "Battery Testing System Team", email = "<EMAIL>"}
]
keywords = ["battery", "testing", "electrochemical", "cycler", "laboratory"]
classifiers = [
    "Development Status :: 3 - Alpha",
    "Intended Audience :: Science/Research",
    "Intended Audience :: Manufacturing",
    "License :: OSI Approved :: MIT License",
    "Operating System :: OS Independent",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.9",
    "Programming Language :: Python :: 3.10",
    "Programming Language :: Python :: 3.11",
    "Programming Language :: Python :: 3.12",
    "Topic :: Scientific/Engineering",
    "Topic :: Scientific/Engineering :: Chemistry",
    "Topic :: System :: Hardware",
]
requires-python = ">=3.9"
dependencies = [
    # Core dependencies
    "pydantic>=2.0.0",
    "pydantic-settings>=2.0.0",
    "sqlalchemy>=2.0.0",
    "alembic>=1.12.0",
    "asyncio-mqtt>=0.16.0",
    "aiofiles>=23.0.0",
    "httpx>=0.25.0",
    
    # GUI Framework
    "PySide6>=6.5.0",
    "pyqtgraph>=0.13.0",
    "qdarkstyle>=3.2.0",
    
    # Data handling
    "pandas>=2.0.0",
    "numpy>=1.24.0",
    "scipy>=1.11.0",
    "h5py>=3.9.0",
    "openpyxl>=3.1.0",
    "xlsxwriter>=3.1.0",
    
    # Database drivers
    "aiosqlite>=0.19.0",
    "asyncpg>=0.28.0",
    "influxdb-client[async]>=1.38.0",
    
    # Hardware communication
    "pymodbus>=3.5.0",
    "pyserial>=3.5",
    "pyserial-asyncio>=0.6",
    "python-can>=4.2.0",
    "nidaqmx>=0.9.0; platform_system=='Windows'",
    
    # Web framework (optional)
    "fastapi>=0.104.0",
    "uvicorn[standard]>=0.24.0",
    "websockets>=12.0",
    
    # Configuration and utilities
    "toml>=0.10.2",
    "pyyaml>=6.0",
    "click>=8.1.0",
    "rich>=13.6.0",
    "structlog>=23.2.0",
    "python-multipart>=0.0.6",
    
    # Security
    "passlib[bcrypt]>=1.7.4",
    "python-jose[cryptography]>=3.3.0",
    
    # Reporting
    "jinja2>=3.1.0",
    "weasyprint>=60.0",
    "matplotlib>=3.7.0",
    "plotly>=5.17.0",
]

[project.optional-dependencies]
dev = [
    "pytest>=7.4.0",
    "pytest-asyncio>=0.21.0",
    "pytest-cov>=4.1.0",
    "pytest-mock>=3.11.0",
    "black>=23.9.0",
    "isort>=5.12.0",
    "flake8>=6.1.0",
    "mypy>=1.6.0",
    "pre-commit>=3.5.0",
]
docs = [
    "sphinx>=7.2.0",
    "sphinx-rtd-theme>=1.3.0",
    "myst-parser>=2.0.0",
]
test = [
    "pytest>=7.4.0",
    "pytest-asyncio>=0.21.0",
    "pytest-cov>=4.1.0",
    "pytest-mock>=3.11.0",
    "factory-boy>=3.3.0",
    "faker>=19.12.0",
]

[project.urls]
Homepage = "https://github.com/bts-team/battery-testing-system"
Documentation = "https://battery-testing-system.readthedocs.io/"
Repository = "https://github.com/bts-team/battery-testing-system.git"
"Bug Tracker" = "https://github.com/bts-team/battery-testing-system/issues"

[project.scripts]
bts = "bts.cli:main"
bts-gui = "bts.gui:main"
bts-server = "bts.web.server:main"

[project.entry-points."bts.drivers"]
arbin = "bts.infrastructure.drivers.arbin:ArbinDriver"
bitrode = "bts.infrastructure.drivers.bitrode:BitrodeDriver"
maccor = "bts.infrastructure.drivers.maccor:MaccorDriver"
neware = "bts.infrastructure.drivers.neware:NewareDriver"
keithley = "bts.infrastructure.drivers.keithley:KeithleyDriver"
mock = "bts.infrastructure.drivers.mock:MockDriver"

[project.entry-points."bts.test_profiles"]
cc_cv = "bts.domain.test_profiles.cc_cv:CCCVProfile"
cp = "bts.domain.test_profiles.cp:CPProfile"
eis = "bts.domain.test_profiles.eis:EISProfile"
pulse = "bts.domain.test_profiles.pulse:PulseProfile"
drive_cycle = "bts.domain.test_profiles.drive_cycle:DriveCycleProfile"

[project.entry-points."bts.exporters"]
csv = "bts.infrastructure.exporters.csv:CSVExporter"
excel = "bts.infrastructure.exporters.excel:ExcelExporter"
json = "bts.infrastructure.exporters.json:JSONExporter"
influxdb = "bts.infrastructure.exporters.influxdb:InfluxDBExporter"

[tool.setuptools.packages.find]
where = ["src"]

[tool.setuptools.package-data]
"bts.gui.resources" = ["*.qss", "*.png", "*.svg", "*.ico"]
"bts.templates" = ["*.html", "*.jinja2"]
"bts.config" = ["*.toml", "*.yaml"]

[tool.black]
line-length = 88
target-version = ['py39', 'py310', 'py311', 'py312']
include = '\.pyi?$'
extend-exclude = '''
/(
  # directories
  \.eggs
  | \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | build
  | dist
)/
'''

[tool.isort]
profile = "black"
multi_line_output = 3
line_length = 88
known_first_party = ["bts"]

[tool.mypy]
python_version = "3.9"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
disallow_incomplete_defs = true
check_untyped_defs = true
disallow_untyped_decorators = true
no_implicit_optional = true
warn_redundant_casts = true
warn_unused_ignores = true
warn_no_return = true
warn_unreachable = true
strict_equality = true

[[tool.mypy.overrides]]
module = [
    "pymodbus.*",
    "nidaqmx.*",
    "can.*",
    "pyqtgraph.*",
    "qdarkstyle.*",
]
ignore_missing_imports = true

[tool.pytest.ini_options]
minversion = "7.0"
addopts = "-ra -q --strict-markers --strict-config"
testpaths = ["tests"]
python_files = ["test_*.py", "*_test.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
markers = [
    "slow: marks tests as slow (deselect with '-m \"not slow\"')",
    "integration: marks tests as integration tests",
    "hardware: marks tests that require hardware",
    "gui: marks tests that require GUI",
]
asyncio_mode = "auto"

[tool.coverage.run]
source = ["src/bts"]
omit = [
    "*/tests/*",
    "*/test_*",
    "*/__pycache__/*",
    "*/migrations/*",
]

[tool.coverage.report]
exclude_lines = [
    "pragma: no cover",
    "def __repr__",
    "if self.debug:",
    "if settings.DEBUG",
    "raise AssertionError",
    "raise NotImplementedError",
    "if 0:",
    "if __name__ == .__main__.:",
    "class .*\\bProtocol\\):",
    "@(abc\\.)?abstractmethod",
]
