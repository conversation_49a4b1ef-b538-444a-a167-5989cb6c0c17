"""Channel entity for battery testing channels."""

from dataclasses import dataclass, field
from datetime import datetime
from enum import Enum
from typing import Dict, Any, Optional, List
from uuid import UUID, uuid4

from .measurement import Measurement
from ..value_objects.safety_limits import SafetyLimits


class ChannelState(Enum):
    """States that a channel can be in."""
    IDLE = "idle"
    RUNNING = "running"
    PAUSED = "paused"
    STOPPED = "stopped"
    ERROR = "error"
    MAINTENANCE = "maintenance"
    DISCONNECTED = "disconnected"


@dataclass
class Channel:
    """
    Represents a single testing channel.
    
    A channel is connected to a specific instrument and can run
    one test at a time. It maintains its own state and safety limits.
    """
    
    # Core identification
    id: str
    name: str
    instrument_id: str
    
    # State management
    state: ChannelState = ChannelState.IDLE
    created_at: datetime = field(default_factory=datetime.utcnow)
    updated_at: datetime = field(default_factory=datetime.utcnow)
    
    # Current test information
    current_test_id: Optional[UUID] = None
    current_step: Optional[int] = None
    step_start_time: Optional[datetime] = None
    
    # Safety and limits
    safety_limits: Optional[SafetyLimits] = None
    
    # Hardware configuration
    hardware_channel: Optional[int] = None  # Physical channel number on instrument
    calibration_data: Optional[Dict[str, Any]] = None
    
    # Status information
    last_measurement: Optional[Measurement] = None
    error_message: Optional[str] = None
    warning_message: Optional[str] = None
    
    # Metadata
    description: Optional[str] = None
    tags: List[str] = field(default_factory=list)
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    def __post_init__(self):
        """Validate channel data after initialization."""
        if not self.id:
            raise ValueError("Channel ID cannot be empty")
        
        if not self.name:
            raise ValueError("Channel name cannot be empty")
        
        if not self.instrument_id:
            raise ValueError("Instrument ID cannot be empty")
    
    @property
    def is_available(self) -> bool:
        """Check if channel is available for new tests."""
        return self.state in [ChannelState.IDLE, ChannelState.STOPPED]
    
    @property
    def is_running(self) -> bool:
        """Check if channel is currently running a test."""
        return self.state == ChannelState.RUNNING
    
    @property
    def is_paused(self) -> bool:
        """Check if channel is paused."""
        return self.state == ChannelState.PAUSED
    
    @property
    def has_error(self) -> bool:
        """Check if channel has an error."""
        return self.state == ChannelState.ERROR
    
    @property
    def is_connected(self) -> bool:
        """Check if channel is connected to its instrument."""
        return self.state != ChannelState.DISCONNECTED
    
    @property
    def current_voltage(self) -> Optional[float]:
        """Get current voltage reading."""
        return self.last_measurement.voltage if self.last_measurement else None
    
    @property
    def current_current(self) -> Optional[float]:
        """Get current current reading."""
        return self.last_measurement.current if self.last_measurement else None
    
    @property
    def current_temperature(self) -> Optional[float]:
        """Get current temperature reading."""
        return self.last_measurement.temperature if self.last_measurement else None
    
    def start_test(self, test_id: UUID) -> None:
        """Start a new test on this channel."""
        if not self.is_available:
            raise ValueError(f"Channel {self.id} is not available for testing")
        
        self.current_test_id = test_id
        self.current_step = 1
        self.step_start_time = datetime.utcnow()
        self.state = ChannelState.RUNNING
        self.error_message = None
        self.warning_message = None
        self._update_timestamp()
    
    def pause_test(self) -> None:
        """Pause the current test."""
        if not self.is_running:
            raise ValueError(f"Channel {self.id} is not running a test")
        
        self.state = ChannelState.PAUSED
        self._update_timestamp()
    
    def resume_test(self) -> None:
        """Resume a paused test."""
        if not self.is_paused:
            raise ValueError(f"Channel {self.id} is not paused")
        
        self.state = ChannelState.RUNNING
        self._update_timestamp()
    
    def stop_test(self) -> None:
        """Stop the current test."""
        if self.state not in [ChannelState.RUNNING, ChannelState.PAUSED]:
            raise ValueError(f"Channel {self.id} is not running a test")
        
        self.current_test_id = None
        self.current_step = None
        self.step_start_time = None
        self.state = ChannelState.STOPPED
        self._update_timestamp()
    
    def complete_test(self) -> None:
        """Mark the current test as completed."""
        if not self.is_running:
            raise ValueError(f"Channel {self.id} is not running a test")
        
        self.current_test_id = None
        self.current_step = None
        self.step_start_time = None
        self.state = ChannelState.IDLE
        self._update_timestamp()
    
    def advance_step(self, step_number: int) -> None:
        """Advance to the next test step."""
        if not self.is_running:
            raise ValueError(f"Channel {self.id} is not running a test")
        
        self.current_step = step_number
        self.step_start_time = datetime.utcnow()
        self._update_timestamp()
    
    def set_error(self, error_message: str) -> None:
        """Set channel to error state with message."""
        self.state = ChannelState.ERROR
        self.error_message = error_message
        self._update_timestamp()
    
    def clear_error(self) -> None:
        """Clear error state and return to idle."""
        if not self.has_error:
            return
        
        self.state = ChannelState.IDLE
        self.error_message = None
        self._update_timestamp()
    
    def set_warning(self, warning_message: str) -> None:
        """Set a warning message."""
        self.warning_message = warning_message
        self._update_timestamp()
    
    def clear_warning(self) -> None:
        """Clear warning message."""
        self.warning_message = None
        self._update_timestamp()
    
    def disconnect(self) -> None:
        """Mark channel as disconnected from instrument."""
        self.state = ChannelState.DISCONNECTED
        self._update_timestamp()
    
    def reconnect(self) -> None:
        """Mark channel as reconnected to instrument."""
        if self.state == ChannelState.DISCONNECTED:
            self.state = ChannelState.IDLE
            self._update_timestamp()
    
    def update_measurement(self, measurement: Measurement) -> None:
        """Update the last measurement for this channel."""
        if measurement.channel_id != self.id:
            raise ValueError("Measurement channel ID does not match this channel")
        
        self.last_measurement = measurement
        self._update_timestamp()
    
    def check_safety_limits(self, measurement: Measurement) -> List[str]:
        """Check if measurement violates safety limits."""
        violations = []
        
        if not self.safety_limits:
            return violations
        
        if (measurement.voltage is not None and 
            self.safety_limits.max_voltage is not None and
            measurement.voltage > self.safety_limits.max_voltage):
            violations.append(f"Voltage {measurement.voltage}V exceeds limit {self.safety_limits.max_voltage}V")
        
        if (measurement.current is not None and 
            self.safety_limits.max_current is not None and
            abs(measurement.current) > self.safety_limits.max_current):
            violations.append(f"Current {measurement.current}A exceeds limit {self.safety_limits.max_current}A")
        
        if (measurement.temperature is not None and 
            self.safety_limits.max_temperature is not None and
            measurement.temperature > self.safety_limits.max_temperature):
            violations.append(f"Temperature {measurement.temperature}°C exceeds limit {self.safety_limits.max_temperature}°C")
        
        return violations
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert channel to dictionary for serialization."""
        data = {
            "id": self.id,
            "name": self.name,
            "instrument_id": self.instrument_id,
            "state": self.state.value,
            "created_at": self.created_at.isoformat(),
            "updated_at": self.updated_at.isoformat(),
            "current_test_id": str(self.current_test_id) if self.current_test_id else None,
            "current_step": self.current_step,
            "step_start_time": self.step_start_time.isoformat() if self.step_start_time else None,
            "hardware_channel": self.hardware_channel,
            "description": self.description,
            "tags": self.tags,
            "metadata": self.metadata,
        }
        
        if self.safety_limits:
            data["safety_limits"] = self.safety_limits.to_dict()
        
        if self.last_measurement:
            data["last_measurement"] = self.last_measurement.to_dict()
        
        if self.error_message:
            data["error_message"] = self.error_message
        
        if self.warning_message:
            data["warning_message"] = self.warning_message
        
        if self.calibration_data:
            data["calibration_data"] = self.calibration_data
        
        return data
    
    def _update_timestamp(self) -> None:
        """Update the last modified timestamp."""
        self.updated_at = datetime.utcnow()
