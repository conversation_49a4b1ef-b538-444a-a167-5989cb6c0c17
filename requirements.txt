# Core dependencies
pydantic>=2.0.0
sqlalchemy>=2.0.0
asyncio-mqtt>=0.13.0
toml>=0.10.2
click>=8.0.0
rich>=13.0.0

# Database drivers
aiosqlite>=0.19.0
asyncpg>=0.28.0  # PostgreSQL
aiomysql>=0.2.0  # MySQL
influxdb-client>=1.36.0  # InfluxDB

# Hardware communication
pymodbus>=3.5.0
pyserial>=3.5
pyserial-asyncio>=0.6
python-can>=4.2.0

# Optional GUI dependencies
PySide6>=6.5.0; extra == "gui"
pyqtgraph>=0.13.0; extra == "gui"
qdarkstyle>=3.1.0; extra == "gui"

# Optional web dependencies
fastapi>=0.100.0; extra == "web"
uvicorn[standard]>=0.23.0; extra == "web"
websockets>=11.0.0; extra == "web"
jinja2>=3.1.0; extra == "web"

# Development dependencies
pytest>=7.4.0; extra == "dev"
pytest-asyncio>=0.21.0; extra == "dev"
pytest-cov>=4.1.0; extra == "dev"
black>=23.7.0; extra == "dev"
isort>=5.12.0; extra == "dev"
flake8>=6.0.0; extra == "dev"
mypy>=1.5.0; extra == "dev"
pre-commit>=3.3.0; extra == "dev"

# Documentation dependencies
sphinx>=7.1.0; extra == "docs"
sphinx-rtd-theme>=1.3.0; extra == "docs"
myst-parser>=2.0.0; extra == "docs"
sphinx-autodoc-typehints>=1.24.0; extra == "docs"

# Hardware-specific drivers
nidaqmx>=0.6.5; extra == "hardware" and platform_system == "Windows"
# Add other hardware-specific dependencies as needed
