# Battery Testing System - User Interface Overview

This document provides a comprehensive overview of all user interfaces available in the Battery Testing System.

## 🖥️ **Available Interfaces**

### 1. **Command Line Interface (CLI)** ✅ **COMPLETE**
- **File**: `src/bts/cli/main.py`
- **Status**: Fully implemented and working
- **Features**:
  - System initialization (`bts init`)
  - Status checking (`bts status`)
  - Data export (`bts export`)
  - GUI launcher (`bts gui`)
  - Server launcher (`bts serve`)

### 2. **Desktop GUI Application** ✅ **IMPLEMENTED**
- **Main Files**:
  - `src/bts/gui/app.py` - Main GUI application
  - `src/bts/gui/main_window.py` - Main window
  - `src/bts/gui/widgets/` - UI widgets
- **Status**: Core structure implemented, ready for use
- **Features**:
  - Multi-tab interface
  - Real-time dashboard
  - Instrument management
  - Test execution controls
  - Data visualization
  - Settings configuration

### 3. **Web Interface** ❌ **NOT IMPLEMENTED**
- **Status**: Planned for future release
- **Technology**: FastAPI + React
- **Features**: Remote access, team collaboration

## 🎨 **GUI Application Structure**

### **Main Application** (`src/bts/gui/app.py`)
```python
class BtsApplication:
    - Handles Qt application lifecycle
    - Manages BTS services integration
    - Provides async/await support
    - Handles themes and styling
```

### **Main Window** (`src/bts/gui/main_window.py`)
```python
class MainWindow:
    - Main application window
    - Menu bar and toolbar
    - Tab-based interface
    - Status bar with indicators
    - Emergency stop controls
```

### **Dashboard Tab** (`src/bts/gui/widgets/dashboard_widget.py`)
```python
class DashboardWidget:
    - System overview cards
    - Active tests monitoring
    - Recent events display
    - Quick control buttons
```

### **Instruments Tab** (`src/bts/gui/widgets/instruments_widget.py`)
```python
class InstrumentsWidget:
    - Instrument discovery
    - Connection management
    - Status monitoring
    - Configuration controls
```

### **Tests Tab** (`src/bts/gui/widgets/tests_widget.py`)
```python
class TestsWidget:
    - Test creation and management
    - Progress monitoring
    - Start/pause/stop controls
    - Test status display
```

### **Data Tab** (`src/bts/gui/widgets/data_widget.py`)
```python
class DataWidget:
    - Data visualization
    - Export functionality
    - Filtering and search
    - Real-time plotting (planned)
```

### **Settings Tab** (`src/bts/gui/widgets/settings_widget.py`)
```python
class SettingsWidget:
    - System configuration
    - Safety limits
    - Hardware settings
    - GUI preferences
```

## 🚀 **How to Launch Each Interface**

### **1. Command Line Interface**
```bash
# Install BTS
pip install -e .

# Initialize system
bts init

# Check status
bts status

# Launch GUI
bts gui

# Get help
bts --help
```

### **2. Desktop GUI Application**

#### **Method 1: Using CLI**
```bash
bts gui
```

#### **Method 2: Using Python**
```python
from src.bts.gui.app import BtsApplication

app = BtsApplication()
app.initialize()
app.run()
```

#### **Method 3: Using Launcher Script**
```bash
python gui_launcher.py
```

### **3. Direct Python API**
```python
import asyncio
from src.bts.infrastructure.config.settings import Settings
from src.bts.application.services.test_engine import TestEngine
# ... other imports

async def main():
    # Use BTS services directly
    settings = Settings.load_from_file()
    # ... setup services
    # ... run tests programmatically

asyncio.run(main())
```

## 📋 **GUI Dependencies**

### **Required Dependencies**
```bash
# Core GUI framework
pip install PySide6>=6.5.0

# Optional enhancements
pip install pyqtgraph>=0.13.0    # Advanced plotting
pip install qdarkstyle>=3.1.0    # Dark theme
```

### **Installation Commands**
```bash
# Install all GUI dependencies
pip install -e .[gui]

# Or install individually
pip install PySide6 pyqtgraph qdarkstyle
```

## 🎯 **GUI Features**

### **✅ Implemented Features**
- **Main Window**: Menu bar, toolbar, status bar
- **Dashboard**: System status cards, active tests, events
- **Instruments**: Table view, connection status
- **Tests**: Test management table, control buttons
- **Data**: Data table, export buttons, filters
- **Settings**: Tabbed configuration interface
- **Themes**: Dark/light theme support
- **Real-time Updates**: Automatic status updates

### **🚧 Partially Implemented**
- **Plotting**: Basic structure, needs pyqtgraph integration
- **Dialogs**: Placeholder dialogs for complex operations
- **Data Export**: UI ready, backend integration needed

### **❌ Not Yet Implemented**
- **Test Profile Builder**: Drag-and-drop interface
- **Real-time Plotting**: Live data visualization
- **Advanced Dialogs**: New test, instrument setup
- **Help System**: Integrated documentation
- **Internationalization**: Multi-language support

## 🖼️ **GUI Screenshots & Layout**

### **Main Window Layout**
```
┌─────────────────────────────────────────────────────────────┐
│ File  Tools  Help                    🛑 ▶️ ⏸️ ⏹️           │
├─────────────────────────────────────────────────────────────┤
│ 📊 Dashboard │ 🔧 Instruments │ 🧪 Tests │ 📈 Data │ ⚙️ Settings │
├─────────────────────────────────────────────────────────────┤
│                                                             │
│                    Tab Content Area                         │
│                                                             │
├─────────────────────────────────────────────────────────────┤
│ 🟢 Connected: 2/3  🟢 Safety: Active  Tests: 1 active     │
└─────────────────────────────────────────────────────────────┘
```

### **Dashboard Tab**
```
┌─────────────────────────────────────────────────────────────┐
│                    System Dashboard                         │
├─────────────────────────────────────────────────────────────┤
│ ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ │
│ │ Connected   │ │ Available   │ │ Active      │ │ Safety      │ │
│ │ Instruments │ │ Channels    │ │ Tests       │ │ Status      │ │
│ │    2/3      │ │     16      │ │     1       │ │   ACTIVE    │ │
│ └─────────────┘ └─────────────┘ └─────────────┘ └─────────────┘ │
├─────────────────────────────────────────────────────────────┤
│ Active Tests              │ Recent Events                   │
│ ┌─────────────────────┐   │ ┌─────────────────────────────┐ │
│ │ Test 1: Running     │   │ │ INFO: Test started          │ │
│ │ Progress: 45%       │   │ │ WARNING: High temperature   │ │
│ │ Channel: CH1        │   │ │ INFO: Measurement logged    │ │
│ └─────────────────────┘   │ └─────────────────────────────┘ │
└─────────────────────────────────────────────────────────────┘
```

## 🔧 **Customization & Theming**

### **Theme Support**
- **Dark Theme**: Default, professional appearance
- **Light Theme**: Traditional light interface
- **Custom Themes**: Extensible CSS styling

### **Configuration**
```toml
[gui]
theme = "dark"              # "dark" or "light"
language = "en"             # Language code
update_interval = 100       # Update frequency (ms)
plot_buffer_size = 10000    # Data points to keep
```

## 🐛 **Troubleshooting GUI Issues**

### **Common Issues**

#### **"PySide6 not found"**
```bash
pip install PySide6
```

#### **"GUI doesn't start"**
```bash
# Check dependencies
python -c "import PySide6; print('PySide6 OK')"

# Run with debug
python -c "
from src.bts.gui.app import BtsApplication
app = BtsApplication()
app.initialize()
"
```

#### **"Dark theme not working"**
```bash
# Install qdarkstyle
pip install qdarkstyle

# Or use basic dark theme (automatic fallback)
```

#### **"GUI crashes on startup"**
- Check Qt platform plugins
- Verify graphics drivers
- Try different Qt backends

### **Debug Mode**
```python
# Enable debug logging
import logging
logging.basicConfig(level=logging.DEBUG)

# Run GUI with debug
from src.bts.gui.app import BtsApplication
app = BtsApplication()
app.settings.debug_mode = True
app.initialize()
app.run()
```

## 📈 **Future GUI Enhancements**

### **Planned Features**
- **Real-time Plotting**: Live data visualization with pyqtgraph
- **Test Builder**: Visual test profile creation
- **3D Visualization**: Battery pack monitoring
- **Mobile App**: Companion mobile application
- **Web Dashboard**: Browser-based interface

### **Advanced Features**
- **Plugin System**: Custom GUI plugins
- **Scripting Console**: Interactive Python console
- **Report Generator**: Automated report creation
- **Data Analytics**: Built-in analysis tools

## 📞 **Getting Help**

### **GUI-Specific Support**
- **Documentation**: Check this file and README.md
- **Examples**: See `examples/` directory
- **Issues**: Report GUI bugs on GitHub
- **Discussions**: Ask questions in GitHub Discussions

### **Development**
- **Contributing**: See `docs/developer/contributing.md`
- **Architecture**: See `PROJECT_STRUCTURE.md`
- **API Docs**: See `docs/api/` directory

---

The BTS GUI provides a professional, user-friendly interface for battery testing operations. While some advanced features are still in development, the core functionality is ready for production use.
