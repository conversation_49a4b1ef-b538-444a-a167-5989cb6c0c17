"""Mock instrument driver for testing and development."""

import asyncio
import random
from datetime import datetime
from typing import Dict, Any, Optional
from uuid import uuid4

from ...domain.entities.measurement import Measurement
from .base import InstrumentDriver, DriverCapabilities, ConnectionType


class MockDriver(InstrumentDriver):
    """
    Mock instrument driver for testing and development.
    
    This driver simulates a battery cycler with realistic behavior
    including voltage/current responses, noise, and drift.
    """
    
    def __init__(self, instrument):
        """Initialize the mock driver."""
        super().__init__(instrument)
        
        # Simulation state
        self._channel_states: Dict[int, Dict[str, Any]] = {}
        self._simulation_running = False
        self._simulation_task: Optional[asyncio.Task] = None
        
        # Initialize channel states
        for channel in range(1, self.capabilities.max_channels + 1):
            self._channel_states[channel] = {
                "enabled": False,
                "mode": "idle",  # idle, cv, cc, cp
                "target_voltage": 0.0,
                "target_current": 0.0,
                "target_power": 0.0,
                "actual_voltage": 0.0,
                "actual_current": 0.0,
                "temperature": 25.0,
                "capacity": 0.0,
                "energy": 0.0,
                "last_update": datetime.utcnow(),
                "noise_level": 0.001,  # 1mV/1mA noise
            }
    
    @property
    def capabilities(self) -> DriverCapabilities:
        """Get the capabilities of the mock driver."""
        return DriverCapabilities(
            supports_voltage_control=True,
            supports_current_control=True,
            supports_power_control=True,
            supports_resistance_control=False,
            can_measure_voltage=True,
            can_measure_current=True,
            can_measure_temperature=True,
            can_measure_power=True,
            can_measure_resistance=True,
            supports_eis=True,
            supports_pulse=True,
            supports_data_logging=True,
            supports_safety_limits=True,
            max_channels=8,
            independent_channels=True,
            voltage_range=(-1.0, 5.0),
            current_range=(-100.0, 100.0),
            voltage_resolution=0.0001,
            current_resolution=0.0001,
            min_sampling_interval=0.01,
            max_sampling_interval=3600.0,
        )
    
    @property
    def connection_type(self) -> ConnectionType:
        """Get the connection type."""
        return ConnectionType.MOCK
    
    async def connect(self) -> bool:
        """Connect to the mock instrument."""
        try:
            self.logger.info("Connecting to mock instrument...")
            
            # Simulate connection delay
            await asyncio.sleep(0.1)
            
            self._connected = True
            self.instrument.state = "connected"
            
            # Start simulation
            await self._start_simulation()
            
            self.logger.info("Connected to mock instrument")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to connect: {e}")
            return False
    
    async def disconnect(self) -> bool:
        """Disconnect from the mock instrument."""
        try:
            self.logger.info("Disconnecting from mock instrument...")
            
            # Stop simulation
            await self._stop_simulation()
            
            self._connected = False
            self.instrument.state = "disconnected"
            
            # Disable all channels
            for channel in range(1, self.capabilities.max_channels + 1):
                self._channel_states[channel]["enabled"] = False
                self._channel_states[channel]["mode"] = "idle"
            
            self.logger.info("Disconnected from mock instrument")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to disconnect: {e}")
            return False
    
    async def identify(self) -> Dict[str, str]:
        """Get mock instrument identification."""
        return {
            "manufacturer": "Mock Instruments Inc.",
            "model": "BTS-MOCK-8000",
            "serial_number": "MOCK001",
            "firmware_version": "1.0.0",
        }
    
    async def reset(self) -> bool:
        """Reset the mock instrument."""
        try:
            self.logger.info("Resetting mock instrument...")
            
            # Reset all channels
            for channel in range(1, self.capabilities.max_channels + 1):
                await self.disable_channel(channel)
            
            self.logger.info("Mock instrument reset complete")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to reset: {e}")
            return False
    
    async def get_status(self) -> Dict[str, Any]:
        """Get mock instrument status."""
        active_channels = sum(1 for state in self._channel_states.values() if state["enabled"])
        
        return {
            "connected": self.is_connected,
            "active_channels": active_channels,
            "total_channels": self.capabilities.max_channels,
            "temperature": 25.0 + random.uniform(-2.0, 2.0),
            "uptime": 12345,  # Mock uptime in seconds
            "errors": [],
            "warnings": [],
        }
    
    async def get_channel_count(self) -> int:
        """Get the number of available channels."""
        return self.capabilities.max_channels
    
    async def enable_channel(self, channel: int) -> bool:
        """Enable a mock channel."""
        if not await self._validate_channel(channel):
            return False
        
        try:
            self._channel_states[channel]["enabled"] = True
            self._channel_states[channel]["mode"] = "idle"
            self.logger.info(f"Enabled channel {channel}")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to enable channel {channel}: {e}")
            return False
    
    async def disable_channel(self, channel: int) -> bool:
        """Disable a mock channel."""
        if not await self._validate_channel(channel):
            return False
        
        try:
            self._channel_states[channel]["enabled"] = False
            self._channel_states[channel]["mode"] = "idle"
            self._channel_states[channel]["target_voltage"] = 0.0
            self._channel_states[channel]["target_current"] = 0.0
            self._channel_states[channel]["target_power"] = 0.0
            self.logger.info(f"Disabled channel {channel}")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to disable channel {channel}: {e}")
            return False
    
    async def set_voltage(self, channel: int, voltage: float) -> bool:
        """Set voltage on a mock channel."""
        if not await self._validate_channel(channel):
            return False
        
        if not await self._validate_voltage(voltage):
            return False
        
        try:
            state = self._channel_states[channel]
            if not state["enabled"]:
                self.logger.error(f"Channel {channel} is not enabled")
                return False
            
            state["target_voltage"] = voltage
            state["mode"] = "cv"  # Constant voltage mode
            self.logger.info(f"Set channel {channel} voltage to {voltage}V")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to set voltage on channel {channel}: {e}")
            return False
    
    async def set_current(self, channel: int, current: float) -> bool:
        """Set current on a mock channel."""
        if not await self._validate_channel(channel):
            return False
        
        if not await self._validate_current(current):
            return False
        
        try:
            state = self._channel_states[channel]
            if not state["enabled"]:
                self.logger.error(f"Channel {channel} is not enabled")
                return False
            
            state["target_current"] = current
            state["mode"] = "cc"  # Constant current mode
            self.logger.info(f"Set channel {channel} current to {current}A")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to set current on channel {channel}: {e}")
            return False
    
    async def set_power(self, channel: int, power: float) -> bool:
        """Set power on a mock channel."""
        if not await self._validate_channel(channel):
            return False
        
        try:
            state = self._channel_states[channel]
            if not state["enabled"]:
                self.logger.error(f"Channel {channel} is not enabled")
                return False
            
            state["target_power"] = power
            state["mode"] = "cp"  # Constant power mode
            self.logger.info(f"Set channel {channel} power to {power}W")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to set power on channel {channel}: {e}")
            return False
    
    async def read_measurement(self, channel: int) -> Optional[Measurement]:
        """Read a measurement from a mock channel."""
        if not await self._validate_channel(channel):
            return None
        
        try:
            state = self._channel_states[channel]
            if not state["enabled"]:
                return None
            
            # Create measurement with simulated values
            measurement = Measurement(
                id=uuid4(),
                timestamp=datetime.utcnow(),
                channel_id=str(channel),
                voltage=state["actual_voltage"],
                current=state["actual_current"],
                temperature=state["temperature"],
                capacity=state["capacity"],
                energy=state["energy"],
                power=state["actual_voltage"] * state["actual_current"],
            )
            
            self._store_measurement(channel, measurement)
            return measurement
            
        except Exception as e:
            self.logger.error(f"Failed to read measurement from channel {channel}: {e}")
            return None
    
    async def read_all_measurements(self) -> Dict[int, Measurement]:
        """Read measurements from all active mock channels."""
        measurements = {}
        
        for channel in range(1, self.capabilities.max_channels + 1):
            if self._channel_states[channel]["enabled"]:
                measurement = await self.read_measurement(channel)
                if measurement:
                    measurements[channel] = measurement
        
        return measurements
    
    async def _start_simulation(self) -> None:
        """Start the simulation task."""
        if not self._simulation_running:
            self._simulation_running = True
            self._simulation_task = asyncio.create_task(self._simulation_loop())
    
    async def _stop_simulation(self) -> None:
        """Stop the simulation task."""
        self._simulation_running = False
        if self._simulation_task:
            self._simulation_task.cancel()
            try:
                await self._simulation_task
            except asyncio.CancelledError:
                pass
            self._simulation_task = None
    
    async def _simulation_loop(self) -> None:
        """Main simulation loop."""
        while self._simulation_running:
            try:
                # Update all enabled channels
                for channel, state in self._channel_states.items():
                    if state["enabled"]:
                        await self._update_channel_simulation(channel, state)
                
                # Sleep for simulation interval
                await asyncio.sleep(0.1)  # 100ms update rate
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                self.logger.error(f"Error in simulation loop: {e}")
    
    async def _update_channel_simulation(self, channel: int, state: Dict[str, Any]) -> None:
        """Update simulation for a single channel."""
        mode = state["mode"]
        noise = state["noise_level"]
        
        if mode == "cv":  # Constant voltage
            # Simulate voltage control with some current response
            state["actual_voltage"] = state["target_voltage"] + random.uniform(-noise, noise)
            # Simple battery model: current decreases over time
            base_current = max(0.1, abs(state["target_voltage"] - 3.7) * 10)
            state["actual_current"] = base_current + random.uniform(-noise*10, noise*10)
            
        elif mode == "cc":  # Constant current
            # Simulate current control with voltage response
            state["actual_current"] = state["target_current"] + random.uniform(-noise*10, noise*10)
            # Simple battery model: voltage changes based on current
            if state["target_current"] > 0:  # Charging
                state["actual_voltage"] = min(4.2, 3.7 + state["target_current"] * 0.1)
            else:  # Discharging
                state["actual_voltage"] = max(2.5, 3.7 + state["target_current"] * 0.05)
            state["actual_voltage"] += random.uniform(-noise, noise)
            
        elif mode == "cp":  # Constant power
            # Simulate power control
            if state["actual_voltage"] > 0:
                state["actual_current"] = state["target_power"] / state["actual_voltage"]
            state["actual_current"] += random.uniform(-noise*10, noise*10)
            
        else:  # Idle
            state["actual_voltage"] = 3.7 + random.uniform(-noise, noise)
            state["actual_current"] = random.uniform(-noise*10, noise*10)
        
        # Update derived values
        dt = 0.1 / 3600  # 0.1 seconds in hours
        state["capacity"] += abs(state["actual_current"]) * dt
        state["energy"] += abs(state["actual_voltage"] * state["actual_current"]) * dt
        
        # Add some temperature variation
        state["temperature"] = 25.0 + random.uniform(-1.0, 1.0)
        
        state["last_update"] = datetime.utcnow()
