"""Safety limits value object for battery testing."""

from dataclasses import dataclass
from typing import Dict, Any, Optional


@dataclass(frozen=True)
class SafetyLimits:
    """
    Immutable value object representing safety limits for battery testing.
    
    These limits are used to ensure safe operation and prevent damage
    to batteries, equipment, or personnel.
    """
    
    # Electrical limits
    max_voltage: Optional[float] = None  # Maximum voltage (V)
    min_voltage: Optional[float] = None  # Minimum voltage (V)
    max_current: Optional[float] = None  # Maximum current magnitude (A)
    max_charge_current: Optional[float] = None  # Maximum charge current (A)
    max_discharge_current: Optional[float] = None  # Maximum discharge current (A)
    max_power: Optional[float] = None  # Maximum power (W)
    
    # Thermal limits
    max_temperature: Optional[float] = None  # Maximum temperature (°C)
    min_temperature: Optional[float] = None  # Minimum temperature (°C)
    max_temperature_rate: Optional[float] = None  # Maximum temperature change rate (°C/min)
    
    # Environmental limits
    max_pressure: Optional[float] = None  # Maximum pressure (bar)
    min_pressure: Optional[float] = None  # Minimum pressure (bar)
    max_humidity: Optional[float] = None  # Maximum humidity (%)
    min_humidity: Optional[float] = None  # Minimum humidity (%)
    
    # Capacity and energy limits
    max_capacity: Optional[float] = None  # Maximum capacity (Ah)
    min_capacity: Optional[float] = None  # Minimum capacity (Ah)
    max_energy: Optional[float] = None  # Maximum energy (Wh)
    
    # Time limits
    max_test_duration: Optional[float] = None  # Maximum test duration (seconds)
    max_step_duration: Optional[float] = None  # Maximum step duration (seconds)
    
    # State limits
    max_soc: Optional[float] = None  # Maximum state of charge (%)
    min_soc: Optional[float] = None  # Minimum state of charge (%)
    min_soh: Optional[float] = None  # Minimum state of health (%)
    
    def __post_init__(self):
        """Validate safety limits after initialization."""
        # Voltage validation
        if (self.max_voltage is not None and self.min_voltage is not None and 
            self.max_voltage <= self.min_voltage):
            raise ValueError("Maximum voltage must be greater than minimum voltage")
        
        if self.max_voltage is not None and self.max_voltage <= 0:
            raise ValueError("Maximum voltage must be positive")
        
        # Current validation
        if self.max_current is not None and self.max_current <= 0:
            raise ValueError("Maximum current must be positive")
        
        if self.max_charge_current is not None and self.max_charge_current <= 0:
            raise ValueError("Maximum charge current must be positive")
        
        if self.max_discharge_current is not None and self.max_discharge_current <= 0:
            raise ValueError("Maximum discharge current must be positive")
        
        # Temperature validation
        if (self.max_temperature is not None and self.min_temperature is not None and 
            self.max_temperature <= self.min_temperature):
            raise ValueError("Maximum temperature must be greater than minimum temperature")
        
        # Pressure validation
        if (self.max_pressure is not None and self.min_pressure is not None and 
            self.max_pressure <= self.min_pressure):
            raise ValueError("Maximum pressure must be greater than minimum pressure")
        
        # Humidity validation
        if self.max_humidity is not None and not (0 <= self.max_humidity <= 100):
            raise ValueError("Maximum humidity must be between 0 and 100")
        
        if self.min_humidity is not None and not (0 <= self.min_humidity <= 100):
            raise ValueError("Minimum humidity must be between 0 and 100")
        
        # SOC validation
        if self.max_soc is not None and not (0 <= self.max_soc <= 100):
            raise ValueError("Maximum SOC must be between 0 and 100")
        
        if self.min_soc is not None and not (0 <= self.min_soc <= 100):
            raise ValueError("Minimum SOC must be between 0 and 100")
        
        if (self.max_soc is not None and self.min_soc is not None and 
            self.max_soc <= self.min_soc):
            raise ValueError("Maximum SOC must be greater than minimum SOC")
        
        # SOH validation
        if self.min_soh is not None and not (0 <= self.min_soh <= 100):
            raise ValueError("Minimum SOH must be between 0 and 100")
        
        # Time validation
        if self.max_test_duration is not None and self.max_test_duration <= 0:
            raise ValueError("Maximum test duration must be positive")
        
        if self.max_step_duration is not None and self.max_step_duration <= 0:
            raise ValueError("Maximum step duration must be positive")
    
    def check_voltage(self, voltage: float) -> bool:
        """Check if voltage is within limits."""
        if self.max_voltage is not None and voltage > self.max_voltage:
            return False
        if self.min_voltage is not None and voltage < self.min_voltage:
            return False
        return True
    
    def check_current(self, current: float) -> bool:
        """Check if current is within limits."""
        if self.max_current is not None and abs(current) > self.max_current:
            return False
        if current > 0 and self.max_charge_current is not None and current > self.max_charge_current:
            return False
        if current < 0 and self.max_discharge_current is not None and abs(current) > self.max_discharge_current:
            return False
        return True
    
    def check_temperature(self, temperature: float) -> bool:
        """Check if temperature is within limits."""
        if self.max_temperature is not None and temperature > self.max_temperature:
            return False
        if self.min_temperature is not None and temperature < self.min_temperature:
            return False
        return True
    
    def check_pressure(self, pressure: float) -> bool:
        """Check if pressure is within limits."""
        if self.max_pressure is not None and pressure > self.max_pressure:
            return False
        if self.min_pressure is not None and pressure < self.min_pressure:
            return False
        return True
    
    def check_soc(self, soc: float) -> bool:
        """Check if state of charge is within limits."""
        if self.max_soc is not None and soc > self.max_soc:
            return False
        if self.min_soc is not None and soc < self.min_soc:
            return False
        return True
    
    def check_soh(self, soh: float) -> bool:
        """Check if state of health is within limits."""
        if self.min_soh is not None and soh < self.min_soh:
            return False
        return True
    
    def get_violations(self, **measurements) -> list[str]:
        """Get list of safety limit violations for given measurements."""
        violations = []
        
        if "voltage" in measurements and not self.check_voltage(measurements["voltage"]):
            violations.append(f"Voltage {measurements['voltage']}V violates limits")
        
        if "current" in measurements and not self.check_current(measurements["current"]):
            violations.append(f"Current {measurements['current']}A violates limits")
        
        if "temperature" in measurements and not self.check_temperature(measurements["temperature"]):
            violations.append(f"Temperature {measurements['temperature']}°C violates limits")
        
        if "pressure" in measurements and not self.check_pressure(measurements["pressure"]):
            violations.append(f"Pressure {measurements['pressure']} bar violates limits")
        
        if "soc" in measurements and not self.check_soc(measurements["soc"]):
            violations.append(f"SOC {measurements['soc']}% violates limits")
        
        if "soh" in measurements and not self.check_soh(measurements["soh"]):
            violations.append(f"SOH {measurements['soh']}% violates limits")
        
        return violations
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert safety limits to dictionary for serialization."""
        return {
            field: getattr(self, field)
            for field in self.__dataclass_fields__
            if getattr(self, field) is not None
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> "SafetyLimits":
        """Create safety limits from dictionary."""
        return cls(**data)
    
    @classmethod
    def default_battery_limits(cls) -> "SafetyLimits":
        """Create default safety limits for typical battery testing."""
        return cls(
            max_voltage=5.0,
            min_voltage=0.0,
            max_current=100.0,
            max_temperature=60.0,
            min_temperature=-20.0,
            max_pressure=2.0,
            max_soc=100.0,
            min_soc=0.0,
            min_soh=50.0,
            max_test_duration=86400 * 30,  # 30 days
        )
    
    @classmethod
    def strict_battery_limits(cls) -> "SafetyLimits":
        """Create strict safety limits for sensitive battery testing."""
        return cls(
            max_voltage=4.5,
            min_voltage=2.0,
            max_current=50.0,
            max_charge_current=25.0,
            max_discharge_current=50.0,
            max_temperature=45.0,
            min_temperature=0.0,
            max_pressure=1.5,
            max_soc=95.0,
            min_soc=5.0,
            min_soh=70.0,
            max_test_duration=86400 * 7,  # 7 days
        )
