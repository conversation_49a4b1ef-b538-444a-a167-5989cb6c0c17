"""Measurement entity for battery testing data."""

from dataclasses import dataclass
from datetime import datetime
from enum import Enum
from typing import Dict, Any, Optional
from uuid import UUID


class MeasurementType(Enum):
    """Types of measurements that can be taken."""
    VOLTAGE = "voltage"
    CURRENT = "current"
    TEMPERATURE = "temperature"
    CAPACITY = "capacity"
    ENERGY = "energy"
    POWER = "power"
    RESISTANCE = "resistance"
    IMPEDANCE = "impedance"
    SOC = "soc"  # State of Charge
    SOH = "soh"  # State of Health
    PRESSURE = "pressure"
    HUMIDITY = "humidity"


@dataclass(frozen=True)
class Measurement:
    """
    Represents a single measurement from a battery test.
    
    This is an immutable value object that contains all the data
    for a single measurement point in time.
    """
    
    # Core identification
    id: UUID
    timestamp: datetime
    channel_id: str
    test_id: Optional[UUID] = None
    
    # Primary measurements
    voltage: Optional[float] = None  # Volts
    current: Optional[float] = None  # Amperes
    temperature: Optional[float] = None  # Celsius
    
    # Derived measurements
    capacity: Optional[float] = None  # Amp-hours
    energy: Optional[float] = None  # Watt-hours
    power: Optional[float] = None  # Watts
    resistance: Optional[float] = None  # Ohms
    
    # State measurements
    soc: Optional[float] = None  # State of Charge (0-100%)
    soh: Optional[float] = None  # State of Health (0-100%)
    
    # Environmental measurements
    pressure: Optional[float] = None  # Bar
    humidity: Optional[float] = None  # Relative humidity (0-100%)
    
    # Metadata
    step_number: Optional[int] = None
    step_time: Optional[float] = None  # Seconds since step start
    cycle_number: Optional[int] = None
    metadata: Optional[Dict[str, Any]] = None
    
    def __post_init__(self):
        """Validate measurement data after initialization."""
        if self.voltage is not None and self.voltage < 0:
            raise ValueError("Voltage cannot be negative")
        
        if self.capacity is not None and self.capacity < 0:
            raise ValueError("Capacity cannot be negative")
        
        if self.energy is not None and self.energy < 0:
            raise ValueError("Energy cannot be negative")
        
        if self.soc is not None and not (0 <= self.soc <= 100):
            raise ValueError("SOC must be between 0 and 100")
        
        if self.soh is not None and not (0 <= self.soh <= 100):
            raise ValueError("SOH must be between 0 and 100")
    
    @property
    def has_primary_data(self) -> bool:
        """Check if measurement has primary voltage/current data."""
        return self.voltage is not None or self.current is not None
    
    @property
    def has_temperature_data(self) -> bool:
        """Check if measurement has temperature data."""
        return self.temperature is not None
    
    @property
    def has_derived_data(self) -> bool:
        """Check if measurement has derived calculations."""
        return any([
            self.capacity is not None,
            self.energy is not None,
            self.power is not None,
            self.resistance is not None,
        ])
    
    @property
    def has_state_data(self) -> bool:
        """Check if measurement has state information."""
        return self.soc is not None or self.soh is not None
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert measurement to dictionary for serialization."""
        data = {
            "id": str(self.id),
            "timestamp": self.timestamp.isoformat(),
            "channel_id": self.channel_id,
            "test_id": str(self.test_id) if self.test_id else None,
        }
        
        # Add non-None values
        for field in [
            "voltage", "current", "temperature", "capacity", "energy",
            "power", "resistance", "soc", "soh", "pressure", "humidity",
            "step_number", "step_time", "cycle_number"
        ]:
            value = getattr(self, field)
            if value is not None:
                data[field] = value
        
        if self.metadata:
            data["metadata"] = self.metadata
        
        return data
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> "Measurement":
        """Create measurement from dictionary."""
        # Convert string IDs back to UUID
        data["id"] = UUID(data["id"])
        if data.get("test_id"):
            data["test_id"] = UUID(data["test_id"])
        
        # Convert timestamp string back to datetime
        if isinstance(data["timestamp"], str):
            data["timestamp"] = datetime.fromisoformat(data["timestamp"])
        
        return cls(**data)
    
    def get_value(self, measurement_type: MeasurementType) -> Optional[float]:
        """Get value for a specific measurement type."""
        type_mapping = {
            MeasurementType.VOLTAGE: self.voltage,
            MeasurementType.CURRENT: self.current,
            MeasurementType.TEMPERATURE: self.temperature,
            MeasurementType.CAPACITY: self.capacity,
            MeasurementType.ENERGY: self.energy,
            MeasurementType.POWER: self.power,
            MeasurementType.RESISTANCE: self.resistance,
            MeasurementType.SOC: self.soc,
            MeasurementType.SOH: self.soh,
            MeasurementType.PRESSURE: self.pressure,
            MeasurementType.HUMIDITY: self.humidity,
        }
        return type_mapping.get(measurement_type)
    
    def with_derived_calculations(
        self,
        capacity: Optional[float] = None,
        energy: Optional[float] = None,
        power: Optional[float] = None,
        resistance: Optional[float] = None,
        soc: Optional[float] = None,
        soh: Optional[float] = None,
    ) -> "Measurement":
        """Create new measurement with derived calculations added."""
        return Measurement(
            id=self.id,
            timestamp=self.timestamp,
            channel_id=self.channel_id,
            test_id=self.test_id,
            voltage=self.voltage,
            current=self.current,
            temperature=self.temperature,
            capacity=capacity or self.capacity,
            energy=energy or self.energy,
            power=power or self.power,
            resistance=resistance or self.resistance,
            soc=soc or self.soc,
            soh=soh or self.soh,
            pressure=self.pressure,
            humidity=self.humidity,
            step_number=self.step_number,
            step_time=self.step_time,
            cycle_number=self.cycle_number,
            metadata=self.metadata,
        )
