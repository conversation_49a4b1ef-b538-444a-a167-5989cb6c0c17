#!/usr/bin/env python3
"""
GUI Launcher for Battery Testing System

This script provides an easy way to launch the BTS GUI application.
It handles dependency checking and provides helpful error messages.
"""

import sys
import os

def check_dependencies():
    """Check if required dependencies are available."""
    missing_deps = []
    
    try:
        import PySide6
    except ImportError:
        missing_deps.append("PySide6")
    
    try:
        import bts
    except ImportError:
        missing_deps.append("battery-testing-system")
    
    return missing_deps

def install_instructions(missing_deps):
    """Provide installation instructions for missing dependencies."""
    print("Missing required dependencies:")
    for dep in missing_deps:
        print(f"  - {dep}")
    
    print("\nTo install missing dependencies:")
    if "PySide6" in missing_deps:
        print("  pip install PySide6")
    if "battery-testing-system" in missing_deps:
        print("  pip install -e .")
    
    print("\nOr install all GUI dependencies:")
    print("  pip install -e .[gui]")

def main():
    """Main launcher function."""
    print("Battery Testing System - GUI Launcher")
    print("=" * 50)
    
    # Check dependencies
    missing_deps = check_dependencies()
    if missing_deps:
        install_instructions(missing_deps)
        return 1
    
    try:
        # Import and run the GUI application
        from src.bts.gui.app import BtsApplication
        
        print("Initializing BTS GUI...")
        app = BtsApplication()
        
        if not app.initialize():
            print("Failed to initialize BTS GUI application")
            return 1
        
        print("Starting BTS GUI application...")
        return app.run()
        
    except ImportError as e:
        print(f"Import error: {e}")
        print("Make sure BTS is properly installed")
        return 1
    except Exception as e:
        print(f"Error starting GUI: {e}")
        return 1

if __name__ == "__main__":
    sys.exit(main())
