# Getting Started with Battery Testing System

This guide will walk you through setting up and running your first battery test with BTS.

## Prerequisites

Before starting, ensure you have:
- BTS installed (see [Installation Guide](installation.md))
- At least one supported instrument or use the mock driver for testing
- Basic understanding of battery testing concepts

## Step 1: System Initialization

First, initialize the BTS system:

```bash
# Initialize database and configuration
bts init

# Verify installation
bts status
```

You should see output similar to:
```
Battery Testing System Status:
  Database: Connected
  Hardware: 0/0 instruments connected
  Channels: 0 available
  Test Engine: Running
  Active Tests: 0
```

## Step 2: Configuration

BTS creates a default configuration file `bts.toml` in your current directory. You can customize it:

```toml
[system]
name = "My Battery Lab"
max_channels = 100
sampling_rate_hz = 1.0

[safety]
global_voltage_limit = 5.0
global_current_limit = 50.0
temperature_limit = 60.0

[gui]
theme = "dark"
language = "en"
```

## Step 3: Add Your First Instrument

### Option A: Using Mock Driver (Recommended for First Test)

The mock driver simulates a battery cycler for testing and learning:

```python
from bts.domain.entities.instrument import Instrument, InstrumentType

# Create mock instrument
mock_cycler = Instrument.create_mock_instrument(
    name="Demo Cycler",
    channels=8
)

print(f"Created instrument: {mock_cycler.name}")
print(f"Channels: {mock_cycler.max_channels}")
print(f"Connection: {mock_cycler.connection_string}")
```

### Option B: Real Hardware

For real hardware, configure according to your instrument:

```python
# Example: Arbin cycler
arbin_cycler = Instrument(
    id="arbin_lab_1",
    name="Arbin BT2000 #1",
    instrument_type=InstrumentType.CYCLER,
    manufacturer="Arbin",
    model="BT2000",
    connection_string="192.168.1.100:502",  # Modbus TCP
    driver_name="arbin",
    max_channels=8,
    max_voltage=5.0,
    max_current=100.0,
)
```

## Step 4: Create a Test Profile

Test profiles define the sequence of steps for your battery test:

```python
from bts.domain.entities.test_profile import TestProfile
from bts.domain.entities.test import TestStep, StepType

# Create a simple CC-CV charge profile
steps = [
    TestStep(
        step_number=1,
        name="CC Charge",
        step_type=StepType.CC,
        target_value=1.0,        # 1A charge current
        limit_value=4.2,         # Stop at 4.2V
        limit_type="voltage",
        description="Charge at 1A until 4.2V"
    ),
    TestStep(
        step_number=2,
        name="CV Charge", 
        step_type=StepType.CV,
        target_value=4.2,        # Hold 4.2V
        limit_value=0.05,        # Stop at 50mA
        limit_type="current",
        description="Hold 4.2V until current drops to 50mA"
    ),
    TestStep(
        step_number=3,
        name="Rest",
        step_type=StepType.REST,
        duration=300,            # 5 minutes
        description="Rest for 5 minutes"
    ),
    TestStep(
        step_number=4,
        name="CC Discharge",
        step_type=StepType.CC,
        target_value=-2.0,       # 2A discharge
        limit_value=2.5,         # Stop at 2.5V
        limit_type="voltage",
        description="Discharge at 2A until 2.5V"
    )
]

# Create profile
profile = TestProfile(
    name="Basic Li-ion Cycle",
    description="Standard CC-CV charge followed by CC discharge",
    profile_type=TestProfileType.CC_CV,
    steps=steps,
    default_cycles=3,
    category="Standard",
    tags=["li-ion", "cc-cv", "basic"]
)

print(f"Created profile: {profile.name}")
print(f"Steps: {len(profile.steps)}")
print(f"Estimated duration: {profile.estimated_duration} seconds")
```

## Step 5: Set Up Safety Limits

Configure safety limits to protect your batteries and equipment:

```python
from bts.domain.value_objects.safety_limits import SafetyLimits

# Create safety limits
safety_limits = SafetyLimits(
    max_voltage=4.5,         # Maximum voltage
    min_voltage=2.0,         # Minimum voltage
    max_current=10.0,        # Maximum current magnitude
    max_temperature=45.0,    # Maximum temperature
    min_temperature=0.0,     # Minimum temperature
    max_soc=95.0,           # Maximum state of charge
    min_soc=5.0,            # Minimum state of charge
)

# Apply to profile
profile.safety_limits = safety_limits
```

## Step 6: Create and Run a Test

Now create a test from your profile and run it:

```python
from bts.domain.entities.test import Test

# Create test
test = Test(
    name="My First Battery Test",
    description="Learning to use BTS",
    profile_id=profile.id,
    steps=profile.steps.copy(),
    total_cycles=1,  # Single cycle for first test
    safety_limits=profile.safety_limits
)

print(f"Created test: {test.name}")
print(f"Total steps: {len(test.steps)}")
print(f"Cycles: {test.total_cycles}")
```

## Step 7: Start the Test Engine

```python
import asyncio
from bts.application.services.hardware_manager import HardwareManager
from bts.application.services.data_manager import DataManager
from bts.application.services.safety_manager import SafetyManager
from bts.application.services.test_engine import TestEngine
from bts.infrastructure.config.settings import Settings

async def run_first_test():
    # Load settings
    settings = Settings.load_from_file()
    
    # Create services
    hardware_manager = HardwareManager()
    data_manager = DataManager(settings.database.dict())
    safety_manager = SafetyManager()
    test_engine = TestEngine(hardware_manager, data_manager, safety_manager)
    
    try:
        # Start services
        await hardware_manager.start()
        await data_manager.start()
        await safety_manager.start()
        await test_engine.start()
        
        # Add instrument
        await hardware_manager.add_instrument(mock_cycler)
        await hardware_manager.connect_instrument(mock_cycler.id)
        
        # Get available channel
        channels = hardware_manager.get_available_channels()
        if not channels:
            print("No channels available!")
            return
        
        channel = channels[0]
        print(f"Using channel: {channel.id}")
        
        # Start test
        success = await test_engine.start_test(test, channel.id)
        if success:
            print("Test started successfully!")
            
            # Monitor test progress
            while test.is_running:
                await asyncio.sleep(1)
                status = await test_engine.get_test_status(test.id)
                print(f"Progress: {status['progress']:.1f}% - Step {status['current_step']}")
                
                # Stop after 30 seconds for demo
                if test.duration and test.duration > 30:
                    await test_engine.stop_test(test.id)
                    print("Demo test stopped after 30 seconds")
                    break
            
            print(f"Test completed with state: {test.state}")
        else:
            print("Failed to start test!")
    
    finally:
        # Cleanup
        await test_engine.stop()
        await safety_manager.stop()
        await data_manager.stop()
        await hardware_manager.stop()

# Run the test
asyncio.run(run_first_test())
```

## Step 8: View Results

After your test completes, you can view the results:

```python
# Export test data
from bts.application.services.data_manager import DataManager

async def export_results():
    data_manager = DataManager(settings.database.dict())
    await data_manager.start()
    
    try:
        # Export to CSV
        success = await data_manager.export_data(
            format_type="csv",
            test_id=test.id,
            output_path="my_first_test.csv"
        )
        
        if success:
            print("Results exported to my_first_test.csv")
        else:
            print("Export failed")
    
    finally:
        await data_manager.stop()

asyncio.run(export_results())
```

## Using the GUI

For a more user-friendly experience, use the GUI:

```bash
# Start the GUI application
bts gui
```

The GUI provides:
- Instrument management interface
- Drag-and-drop test profile builder
- Real-time plotting and monitoring
- Test execution controls
- Data export and reporting tools

## Using the Web Interface

For remote access or team collaboration:

```bash
# Start web server
bts serve --host 0.0.0.0 --port 8000
```

Then open your browser to `http://localhost:8000`

## Next Steps

Now that you've run your first test, explore these advanced features:

1. **[Test Profile Creation](test_profiles.md)** - Learn to create complex test profiles
2. **[Hardware Setup](hardware_setup.md)** - Configure real instruments
3. **[Data Export](data_export.md)** - Advanced data analysis and reporting
4. **[Safety Configuration](safety.md)** - Set up comprehensive safety systems
5. **[Scripting API](../api/scripting.md)** - Write custom test scripts

## Troubleshooting

### Common Issues

**"No channels available"**
- Ensure your instrument is connected and recognized
- Check instrument configuration and driver settings
- Verify network connectivity for remote instruments

**"Test failed to start"**
- Check safety limits are not too restrictive
- Verify channel is not already in use
- Review instrument status and error messages

**"Connection failed"**
- Verify instrument IP address and port
- Check network connectivity
- Ensure instrument is powered on and ready

**"Permission denied"**
- Check user role and permissions
- Verify file system permissions for data directory
- Ensure database is writable

### Getting Help

- Check the [FAQ](../faq.md)
- Review [API Documentation](../api/index.md)
- Post questions in [GitHub Discussions](https://github.com/bts-team/battery-testing-system/discussions)
- Contact support: <EMAIL>

## Summary

You've successfully:
✅ Initialized BTS
✅ Created a test profile
✅ Set up safety limits
✅ Run your first test
✅ Exported results

You're now ready to start testing real batteries with BTS!
