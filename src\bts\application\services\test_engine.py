"""Test execution engine for battery testing operations."""

import asyncio
import logging
from datetime import datetime
from typing import Dict, List, Optional, Set, Callable, Any
from uuid import UUID

from ...domain.entities.test import Test, TestState, TestStep, StepType
from ...domain.entities.channel import Channel, ChannelState
from ...domain.entities.measurement import Measurement
from ...domain.entities.test_profile import TestProfile
from .data_manager import DataManager
from .safety_manager import SafetyManager
from .hardware_manager import HardwareManager


class TestExecutionError(Exception):
    """Exception raised during test execution."""
    pass


class TestEngine:
    """
    Core test execution engine for battery testing.
    
    This service orchestrates test execution across multiple channels,
    manages test state transitions, and coordinates with other services.
    """
    
    def __init__(
        self,
        hardware_manager: HardwareManager,
        data_manager: DataManager,
        safety_manager: SafetyManager,
    ):
        """Initialize the test engine."""
        self.hardware_manager = hardware_manager
        self.data_manager = data_manager
        self.safety_manager = safety_manager
        self.logger = logging.getLogger(__name__)
        
        # Test execution state
        self._running_tests: Dict[UUID, Test] = {}
        self._test_tasks: Dict[UUID, asyncio.Task] = {}
        self._channel_assignments: Dict[str, UUID] = {}  # channel_id -> test_id
        
        # Event callbacks
        self._test_started_callbacks: List[Callable[[Test], None]] = []
        self._test_completed_callbacks: List[Callable[[Test], None]] = []
        self._test_error_callbacks: List[Callable[[Test, str], None]] = []
        self._measurement_callbacks: List[Callable[[Measurement], None]] = []
        
        # Engine state
        self._running = False
        self._engine_task: Optional[asyncio.Task] = None
    
    @property
    def is_running(self) -> bool:
        """Check if the test engine is running."""
        return self._running
    
    @property
    def active_test_count(self) -> int:
        """Get the number of active tests."""
        return len(self._running_tests)
    
    @property
    def active_tests(self) -> List[Test]:
        """Get list of active tests."""
        return list(self._running_tests.values())
    
    async def start(self) -> None:
        """Start the test engine."""
        if self._running:
            return
        
        self.logger.info("Starting test engine...")
        self._running = True
        
        # Start the main engine loop
        self._engine_task = asyncio.create_task(self._engine_loop())
        
        self.logger.info("Test engine started")
    
    async def stop(self) -> None:
        """Stop the test engine and all running tests."""
        if not self._running:
            return
        
        self.logger.info("Stopping test engine...")
        self._running = False
        
        # Stop all running tests
        for test_id in list(self._running_tests.keys()):
            await self.stop_test(test_id)
        
        # Cancel the engine task
        if self._engine_task:
            self._engine_task.cancel()
            try:
                await self._engine_task
            except asyncio.CancelledError:
                pass
            self._engine_task = None
        
        self.logger.info("Test engine stopped")
    
    async def start_test(self, test: Test, channel_id: str) -> bool:
        """
        Start a test on a specific channel.
        
        Args:
            test: Test to start
            channel_id: Channel ID to run the test on
            
        Returns:
            bool: True if test started successfully, False otherwise
        """
        try:
            # Validate test can be started
            if not test.can_start:
                raise TestExecutionError(f"Test {test.id} cannot be started in state {test.state}")
            
            # Check if channel is available
            if channel_id in self._channel_assignments:
                raise TestExecutionError(f"Channel {channel_id} is already in use")
            
            # Get channel and validate it's available
            channel = await self.hardware_manager.get_channel(channel_id)
            if not channel or not channel.is_available:
                raise TestExecutionError(f"Channel {channel_id} is not available")
            
            # Start the test
            test.start(channel_id)
            channel.start_test(test.id)
            
            # Register the test
            self._running_tests[test.id] = test
            self._channel_assignments[channel_id] = test.id
            
            # Create and start the test execution task
            task = asyncio.create_task(self._execute_test(test, channel))
            self._test_tasks[test.id] = task
            
            # Save test state
            await self.data_manager.save_test(test)
            
            # Notify callbacks
            for callback in self._test_started_callbacks:
                try:
                    callback(test)
                except Exception as e:
                    self.logger.error(f"Error in test started callback: {e}")
            
            self.logger.info(f"Started test {test.id} on channel {channel_id}")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to start test {test.id}: {e}")
            test.set_error(str(e))
            await self.data_manager.save_test(test)
            return False
    
    async def stop_test(self, test_id: UUID) -> bool:
        """
        Stop a running test.
        
        Args:
            test_id: ID of the test to stop
            
        Returns:
            bool: True if test stopped successfully, False otherwise
        """
        try:
            test = self._running_tests.get(test_id)
            if not test:
                self.logger.warning(f"Test {test_id} not found in running tests")
                return False
            
            # Stop the test
            test.stop()
            
            # Stop the channel
            if test.channel_id:
                channel = await self.hardware_manager.get_channel(test.channel_id)
                if channel:
                    channel.stop_test()
            
            # Cancel the execution task
            task = self._test_tasks.get(test_id)
            if task:
                task.cancel()
                try:
                    await task
                except asyncio.CancelledError:
                    pass
                del self._test_tasks[test_id]
            
            # Clean up
            del self._running_tests[test_id]
            if test.channel_id and test.channel_id in self._channel_assignments:
                del self._channel_assignments[test.channel_id]
            
            # Save test state
            await self.data_manager.save_test(test)
            
            self.logger.info(f"Stopped test {test_id}")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to stop test {test_id}: {e}")
            return False
    
    async def pause_test(self, test_id: UUID) -> bool:
        """
        Pause a running test.
        
        Args:
            test_id: ID of the test to pause
            
        Returns:
            bool: True if test paused successfully, False otherwise
        """
        try:
            test = self._running_tests.get(test_id)
            if not test or not test.can_pause:
                return False
            
            test.pause()
            
            # Pause the channel
            if test.channel_id:
                channel = await self.hardware_manager.get_channel(test.channel_id)
                if channel:
                    channel.pause_test()
            
            await self.data_manager.save_test(test)
            self.logger.info(f"Paused test {test_id}")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to pause test {test_id}: {e}")
            return False
    
    async def resume_test(self, test_id: UUID) -> bool:
        """
        Resume a paused test.
        
        Args:
            test_id: ID of the test to resume
            
        Returns:
            bool: True if test resumed successfully, False otherwise
        """
        try:
            test = self._running_tests.get(test_id)
            if not test or not test.can_resume:
                return False
            
            test.resume()
            
            # Resume the channel
            if test.channel_id:
                channel = await self.hardware_manager.get_channel(test.channel_id)
                if channel:
                    channel.resume_test()
            
            await self.data_manager.save_test(test)
            self.logger.info(f"Resumed test {test_id}")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to resume test {test_id}: {e}")
            return False
    
    async def get_test_status(self, test_id: UUID) -> Optional[Dict[str, Any]]:
        """Get the status of a test."""
        test = self._running_tests.get(test_id)
        if not test:
            return None
        
        channel = None
        if test.channel_id:
            channel = await self.hardware_manager.get_channel(test.channel_id)
        
        return {
            "test_id": str(test.id),
            "state": test.state.value,
            "progress": test.progress_percentage,
            "current_step": test.current_step,
            "current_cycle": test.current_cycle,
            "total_cycles": test.total_cycles,
            "duration": test.duration,
            "channel_id": test.channel_id,
            "channel_state": channel.state.value if channel else None,
            "last_measurement": channel.last_measurement.to_dict() if channel and channel.last_measurement else None,
            "error_message": test.error_message,
            "warning_messages": test.warning_messages,
        }
    
    def add_test_started_callback(self, callback: Callable[[Test], None]) -> None:
        """Add a callback for when tests are started."""
        self._test_started_callbacks.append(callback)
    
    def add_test_completed_callback(self, callback: Callable[[Test], None]) -> None:
        """Add a callback for when tests are completed."""
        self._test_completed_callbacks.append(callback)
    
    def add_test_error_callback(self, callback: Callable[[Test, str], None]) -> None:
        """Add a callback for when tests encounter errors."""
        self._test_error_callbacks.append(callback)
    
    def add_measurement_callback(self, callback: Callable[[Measurement], None]) -> None:
        """Add a callback for new measurements."""
        self._measurement_callbacks.append(callback)
    
    async def _engine_loop(self) -> None:
        """Main engine loop for monitoring tests."""
        while self._running:
            try:
                # Check for completed tasks
                completed_tasks = []
                for test_id, task in self._test_tasks.items():
                    if task.done():
                        completed_tasks.append(test_id)
                
                # Clean up completed tasks
                for test_id in completed_tasks:
                    await self._cleanup_completed_test(test_id)
                
                # Sleep for a short interval
                await asyncio.sleep(0.1)
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                self.logger.error(f"Error in engine loop: {e}")
    
    async def _execute_test(self, test: Test, channel: Channel) -> None:
        """Execute a test on a channel."""
        try:
            self.logger.info(f"Executing test {test.id} on channel {channel.id}")
            
            # Execute all cycles
            for cycle in range(test.current_cycle, test.total_cycles + 1):
                test.current_cycle = cycle
                
                # Execute all steps in the cycle
                for step_num in range(test.current_step or 1, len(test.steps) + 1):
                    if not test.is_running:
                        return  # Test was stopped or paused
                    
                    step = test.steps[step_num - 1]
                    test.current_step = step_num
                    test.step_start_time = datetime.utcnow()
                    channel.advance_step(step_num)
                    
                    # Execute the step
                    await self._execute_step(test, channel, step)
                    
                    # Save progress
                    await self.data_manager.save_test(test)
                
                # Reset step for next cycle
                test.current_step = 1
            
            # Test completed successfully
            test.complete()
            channel.complete_test()
            await self.data_manager.save_test(test)
            
            # Notify callbacks
            for callback in self._test_completed_callbacks:
                try:
                    callback(test)
                except Exception as e:
                    self.logger.error(f"Error in test completed callback: {e}")
            
            self.logger.info(f"Test {test.id} completed successfully")
            
        except Exception as e:
            self.logger.error(f"Error executing test {test.id}: {e}")
            test.set_error(str(e))
            channel.set_error(str(e))
            await self.data_manager.save_test(test)
            
            # Notify error callbacks
            for callback in self._test_error_callbacks:
                try:
                    callback(test, str(e))
                except Exception as e:
                    self.logger.error(f"Error in test error callback: {e}")
    
    async def _execute_step(self, test: Test, channel: Channel, step: TestStep) -> None:
        """Execute a single test step."""
        self.logger.info(f"Executing step {step.step_number}: {step.name}")
        
        # Apply step settings to hardware
        driver = await self.hardware_manager.get_driver(channel.instrument_id)
        if not driver:
            raise TestExecutionError(f"No driver found for instrument {channel.instrument_id}")
        
        # Set the appropriate control mode
        if step.step_type == StepType.CC:
            await driver.set_current(channel.hardware_channel or 1, step.target_value or 0.0)
        elif step.step_type == StepType.CV:
            await driver.set_voltage(channel.hardware_channel or 1, step.target_value or 0.0)
        elif step.step_type == StepType.CP:
            await driver.set_power(channel.hardware_channel or 1, step.target_value or 0.0)
        elif step.step_type == StepType.REST:
            await driver.set_current(channel.hardware_channel or 1, 0.0)
        
        # Monitor step execution
        step_start_time = datetime.utcnow()
        
        while test.is_running:
            # Read measurement
            measurement = await driver.read_measurement(channel.hardware_channel or 1)
            if measurement:
                measurement.test_id = test.id
                measurement.step_number = step.step_number
                measurement.cycle_number = test.current_cycle
                
                # Update channel
                channel.update_measurement(measurement)
                
                # Check safety limits
                violations = channel.check_safety_limits(measurement)
                if violations:
                    raise TestExecutionError(f"Safety violation: {', '.join(violations)}")
                
                # Save measurement
                await self.data_manager.save_measurement(measurement)
                
                # Notify callbacks
                for callback in self._measurement_callbacks:
                    try:
                        callback(measurement)
                    except Exception as e:
                        self.logger.error(f"Error in measurement callback: {e}")
            
            # Check step end conditions
            current_time = datetime.utcnow()
            step_duration = (current_time - step_start_time).total_seconds()
            
            # Check time limit
            if step.duration and step_duration >= step.duration:
                break
            
            # Check other end conditions
            if measurement and await self._check_step_end_conditions(step, measurement):
                break
            
            # Wait before next measurement
            await asyncio.sleep(1.0)  # 1 second sampling rate
    
    async def _check_step_end_conditions(self, step: TestStep, measurement: Measurement) -> bool:
        """Check if step end conditions are met."""
        if not step.limit_value or not step.limit_type:
            return False
        
        if step.limit_type == "voltage":
            if step.step_type == StepType.CC and measurement.voltage:
                return measurement.voltage >= step.limit_value
        elif step.limit_type == "current":
            if step.step_type == StepType.CV and measurement.current:
                return abs(measurement.current) <= step.limit_value
        elif step.limit_type == "capacity":
            if measurement.capacity:
                return measurement.capacity >= step.limit_value
        
        return False
    
    async def _cleanup_completed_test(self, test_id: UUID) -> None:
        """Clean up a completed test."""
        try:
            test = self._running_tests.get(test_id)
            task = self._test_tasks.get(test_id)
            
            if task:
                try:
                    await task
                except Exception as e:
                    self.logger.error(f"Test {test_id} task failed: {e}")
                    if test:
                        test.set_error(str(e))
                        await self.data_manager.save_test(test)
                
                del self._test_tasks[test_id]
            
            if test:
                # Clean up channel assignment
                if test.channel_id and test.channel_id in self._channel_assignments:
                    del self._channel_assignments[test.channel_id]
                
                del self._running_tests[test_id]
                
                self.logger.info(f"Cleaned up test {test_id}")
            
        except Exception as e:
            self.logger.error(f"Error cleaning up test {test_id}: {e}")
