# Battery Testing System - Project Structure

This document provides a comprehensive overview of the project structure and architecture.

## Directory Structure

```
battery-testing-system/
├── src/bts/                          # Main source code
│   ├── __init__.py                   # Package initialization
│   ├── domain/                       # Domain layer (business logic)
│   │   ├── __init__.py
│   │   ├── entities/                 # Domain entities
│   │   │   ├── __init__.py
│   │   │   ├── measurement.py        # Measurement entity
│   │   │   ├── channel.py            # Channel entity
│   │   │   ├── test.py               # Test entity
│   │   │   ├── test_profile.py       # Test profile entity
│   │   │   ├── instrument.py         # Instrument entity
│   │   │   └── user.py               # User entity
│   │   └── value_objects/            # Value objects
│   │       ├── __init__.py
│   │       └── safety_limits.py      # Safety limits value object
│   ├── application/                  # Application layer (use cases)
│   │   ├── __init__.py
│   │   └── services/                 # Application services
│   │       ├── __init__.py
│   │       ├── test_engine.py        # Test execution engine
│   │       ├── data_manager.py       # Data management service
│   │       ├── safety_manager.py     # Safety monitoring service
│   │       └── hardware_manager.py   # Hardware management service
│   ├── infrastructure/               # Infrastructure layer (external concerns)
│   │   ├── __init__.py
│   │   ├── drivers/                  # Hardware drivers
│   │   │   ├── __init__.py
│   │   │   ├── base.py               # Abstract driver base class
│   │   │   ├── mock.py               # Mock driver for testing
│   │   │   └── arbin.py              # Arbin cycler driver
│   │   ├── database/                 # Database infrastructure
│   │   │   ├── __init__.py
│   │   │   ├── connection.py         # Database connection management
│   │   │   └── models.py             # SQLAlchemy models
│   │   └── config/                   # Configuration management
│   │       ├── __init__.py
│   │       └── settings.py           # Settings and configuration
│   └── cli/                          # Command line interface
│       ├── __init__.py
│       └── main.py                   # CLI entry point
├── tests/                            # Test suite
│   ├── __init__.py
│   ├── conftest.py                   # Pytest configuration and fixtures
│   ├── unit/                         # Unit tests
│   │   ├── __init__.py
│   │   ├── test_domain_entities.py   # Domain entity tests
│   │   └── test_mock_driver.py       # Mock driver tests
│   └── integration/                  # Integration tests
│       ├── __init__.py
│       └── test_system_integration.py # System integration tests
├── examples/                         # Example scripts and tutorials
│   ├── basic_test_example.py         # Basic test execution example
│   └── custom_test_profile.py        # Custom test profile examples
├── docs/                             # Documentation
│   └── user/                         # User documentation
│       ├── installation.md           # Installation guide
│       └── getting_started.md        # Getting started guide
├── config/                           # Configuration files
│   └── default.toml                  # Default configuration
├── data/                             # Data directory (created at runtime)
├── logs/                             # Log files (created at runtime)
├── README.md                         # Project overview and quick start
├── CHANGELOG.md                      # Version history and changes
├── requirements.txt                  # Python dependencies
├── bts_pyproject.toml                # Project configuration and metadata
└── LICENSE                           # MIT license
```

## Architecture Overview

The Battery Testing System follows Clean Architecture principles with clear separation of concerns:

### Domain Layer (`src/bts/domain/`)
- **Entities**: Core business objects with identity and lifecycle
  - `Measurement`: Individual data points from instruments
  - `Channel`: Testing channels with state management
  - `Test`: Test execution with progress tracking
  - `TestProfile`: Reusable test templates
  - `Instrument`: Hardware instrument representation
  - `User`: User management with roles and permissions

- **Value Objects**: Immutable objects representing concepts
  - `SafetyLimits`: Safety constraints and validation

### Application Layer (`src/bts/application/`)
- **Services**: Orchestrate business logic and coordinate between layers
  - `TestEngine`: Core test execution and monitoring
  - `DataManager`: Data persistence and retrieval
  - `SafetyManager`: Real-time safety monitoring
  - `HardwareManager`: Instrument and driver management

### Infrastructure Layer (`src/bts/infrastructure/`)
- **Drivers**: Hardware abstraction layer
  - `InstrumentDriver`: Abstract base class for all drivers
  - `MockDriver`: Simulation driver for development/testing
  - `ArbinDriver`: Arbin cycler driver implementation

- **Database**: Data persistence infrastructure
  - `DatabaseConnection`: Async database connection management
  - `Models`: SQLAlchemy ORM models

- **Configuration**: System configuration management
  - `Settings`: Hierarchical configuration with validation

### Interface Layer (`src/bts/cli/`)
- **CLI**: Command-line interface for system control
  - `main.py`: Click-based CLI with rich formatting

## Key Design Patterns

### 1. Clean Architecture
- **Dependency Inversion**: Inner layers don't depend on outer layers
- **Separation of Concerns**: Each layer has a single responsibility
- **Testability**: Easy to test with dependency injection

### 2. Repository Pattern
- Abstract data access through interfaces
- Swappable database backends
- Consistent data access patterns

### 3. Strategy Pattern
- Pluggable hardware drivers
- Configurable test profiles
- Extensible export formats

### 4. Observer Pattern
- Event-driven safety monitoring
- Real-time data streaming
- Progress notifications

### 5. Factory Pattern
- Driver instantiation
- Test profile creation
- Configuration loading

## Data Flow

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   CLI/GUI       │    │   Web API       │    │   Scripts       │
└─────────┬───────┘    └─────────┬───────┘    └─────────┬───────┘
          │                      │                      │
          └──────────────────────┼──────────────────────┘
                                 │
                    ┌─────────────▼───────────────┐
                    │     Application Layer      │
                    │  ┌─────────────────────────┐│
                    │  │     Test Engine         ││
                    │  │   Data Manager          ││
                    │  │   Safety Manager        ││
                    │  │   Hardware Manager      ││
                    │  └─────────────────────────┘│
                    └─────────────┬───────────────┘
                                  │
                    ┌─────────────▼───────────────┐
                    │      Domain Layer           │
                    │  ┌─────────────────────────┐│
                    │  │    Entities             ││
                    │  │    Value Objects        ││
                    │  │    Business Logic       ││
                    │  └─────────────────────────┘│
                    └─────────────┬───────────────┘
                                  │
                    ┌─────────────▼───────────────┐
                    │   Infrastructure Layer     │
                    │  ┌─────────────────────────┐│
                    │  │    Hardware Drivers     ││
                    │  │    Database Access      ││
                    │  │    Configuration        ││
                    │  │    External Services    ││
                    │  └─────────────────────────┘│
                    └─────────────────────────────┘
```

## Testing Strategy

### Unit Tests (`tests/unit/`)
- Test individual components in isolation
- Mock external dependencies
- Fast execution for development feedback
- Target: >90% code coverage

### Integration Tests (`tests/integration/`)
- Test component interactions
- Use real database connections
- Test complete workflows
- Validate system behavior

### Test Organization
- **Fixtures**: Reusable test data and setup in `conftest.py`
- **Markers**: Categorize tests (slow, integration, hardware, gui)
- **Async Support**: Full async/await testing with pytest-asyncio
- **Coverage**: Comprehensive coverage reporting

## Configuration Management

### Hierarchical Configuration
1. **Default values**: Built into the code
2. **Configuration files**: TOML files with environment-specific settings
3. **Environment variables**: Override for deployment
4. **Command-line arguments**: Override for specific runs

### Configuration Structure
```toml
[system]
name = "Battery Testing System"
max_channels = 1000

[database]
primary = "sqlite:///data/bts.db"
timeseries = "influxdb://localhost:8086/bts"

[safety]
global_voltage_limit = 5.0
emergency_stop_enabled = true

[hardware]
auto_discovery = true
connection_timeout = 30

[gui]
theme = "dark"
update_interval = 100
```

## Extensibility Points

### 1. Hardware Drivers
- Implement `InstrumentDriver` interface
- Register via entry points
- Support for any communication protocol

### 2. Test Profiles
- Create custom test step sequences
- Define complex end conditions
- Reusable across different tests

### 3. Data Exporters
- Custom export formats
- Integration with external systems
- Configurable data transformations

### 4. Safety Monitors
- Custom safety checks
- External system integration
- Configurable response actions

## Development Workflow

### 1. Setup Development Environment
```bash
git clone <repository>
cd battery-testing-system
python -m venv venv
source venv/bin/activate
pip install -e ".[dev,test]"
```

### 2. Code Quality Tools
- **Black**: Code formatting
- **isort**: Import sorting
- **flake8**: Linting
- **mypy**: Type checking
- **pre-commit**: Git hooks

### 3. Testing
```bash
# Run all tests
pytest

# Run with coverage
pytest --cov=bts --cov-report=html

# Run specific test categories
pytest -m "not slow"
pytest tests/unit/
```

### 4. Documentation
- **Sphinx**: API documentation generation
- **Markdown**: User guides and tutorials
- **Type hints**: Self-documenting code

## Deployment Options

### 1. Local Installation
- Direct Python package installation
- Virtual environment isolation
- Local database (SQLite)

### 2. Server Deployment
- Web interface with FastAPI
- Production database (PostgreSQL/InfluxDB)
- Multi-user support

### 3. Container Deployment
- Docker containerization
- Kubernetes orchestration
- Scalable and portable

### 4. Cloud Deployment
- Cloud-native services
- Managed databases
- Auto-scaling capabilities

## Performance Considerations

### 1. Async I/O
- Non-blocking hardware communication
- Concurrent test execution
- Efficient resource utilization

### 2. Database Optimization
- Separate databases for metadata and time-series data
- Connection pooling
- Batch operations for measurements

### 3. Memory Management
- Streaming data processing
- Configurable buffer sizes
- Garbage collection optimization

### 4. Scalability
- Horizontal scaling support
- Load balancing capabilities
- Distributed testing coordination

## Security Features

### 1. Authentication
- User account management
- Password security requirements
- Session management

### 2. Authorization
- Role-based access control
- Permission-based operations
- Audit trail logging

### 3. Data Protection
- Database encryption
- Secure communication protocols
- Data backup and recovery

### 4. Safety Integration
- Emergency stop mechanisms
- Safety limit enforcement
- External safety system integration

---

This project structure provides a solid foundation for a professional battery testing system with room for growth and customization.
