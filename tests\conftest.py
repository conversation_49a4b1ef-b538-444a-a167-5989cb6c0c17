"""Pytest configuration and fixtures for the Battery Testing System tests."""

import asyncio
import pytest
import tempfile
from datetime import datetime
from pathlib import Path
from uuid import uuid4

from bts.domain.entities.instrument import Instrument, InstrumentType
from bts.domain.entities.channel import Channel
from bts.domain.entities.test import Test, TestStep, StepType
from bts.domain.entities.test_profile import TestProfile, TestProfileType
from bts.domain.entities.measurement import Measurement
from bts.domain.value_objects.safety_limits import SafetyLimits
from bts.infrastructure.config.settings import Settings
from bts.infrastructure.drivers.mock import MockDriver


@pytest.fixture(scope="session")
def event_loop():
    """Create an instance of the default event loop for the test session."""
    loop = asyncio.get_event_loop_policy().new_event_loop()
    yield loop
    loop.close()


@pytest.fixture
def temp_dir():
    """Create a temporary directory for test files."""
    with tempfile.TemporaryDirectory() as tmpdir:
        yield Path(tmpdir)


@pytest.fixture
def test_settings(temp_dir):
    """Create test settings with temporary database paths."""
    settings = Settings()
    settings.database.primary = f"sqlite:///{temp_dir}/test_primary.db"
    settings.database.timeseries = f"sqlite:///{temp_dir}/test_timeseries.db"
    settings.debug_mode = True
    settings.log_level = "DEBUG"
    return settings


@pytest.fixture
def mock_instrument():
    """Create a mock instrument for testing."""
    return Instrument(
        id="test_instrument_1",
        name="Test Cycler",
        instrument_type=InstrumentType.CYCLER,
        manufacturer="Test Corp",
        model="TC-1000",
        serial_number="TC001",
        connection_string="mock://localhost",
        driver_name="mock",
        max_channels=4,
        max_voltage=5.0,
        max_current=100.0,
        max_power=500.0,
        voltage_resolution=0.001,
        current_resolution=0.001,
    )


@pytest.fixture
def mock_channel(mock_instrument):
    """Create a mock channel for testing."""
    return Channel(
        id="test_channel_1",
        name="Test Channel 1",
        instrument_id=mock_instrument.id,
        hardware_channel=1,
        safety_limits=SafetyLimits.default_battery_limits(),
    )


@pytest.fixture
async def mock_driver(mock_instrument):
    """Create and connect a mock driver for testing."""
    driver = MockDriver(mock_instrument)
    await driver.connect()
    yield driver
    await driver.disconnect()


@pytest.fixture
def sample_measurement():
    """Create a sample measurement for testing."""
    return Measurement(
        id=uuid4(),
        timestamp=datetime.utcnow(),
        channel_id="test_channel_1",
        test_id=uuid4(),
        voltage=3.7,
        current=1.0,
        temperature=25.0,
        capacity=1.5,
        energy=5.55,
        power=3.7,
        step_number=1,
        cycle_number=1,
    )


@pytest.fixture
def test_step_cc():
    """Create a constant current test step."""
    return TestStep(
        step_number=1,
        name="CC Charge",
        step_type=StepType.CC,
        target_value=1.0,
        limit_value=4.2,
        limit_type="voltage",
        duration=3600,
        description="Charge at 1A until 4.2V",
    )


@pytest.fixture
def test_step_cv():
    """Create a constant voltage test step."""
    return TestStep(
        step_number=2,
        name="CV Charge",
        step_type=StepType.CV,
        target_value=4.2,
        limit_value=0.05,
        limit_type="current",
        duration=7200,
        description="Hold 4.2V until current drops to 50mA",
    )


@pytest.fixture
def test_step_rest():
    """Create a rest test step."""
    return TestStep(
        step_number=3,
        name="Rest",
        step_type=StepType.REST,
        duration=300,
        description="Rest for 5 minutes",
    )


@pytest.fixture
def sample_test_profile(test_step_cc, test_step_cv, test_step_rest):
    """Create a sample test profile."""
    return TestProfile(
        name="Test CC-CV Profile",
        description="Test constant current - constant voltage profile",
        profile_type=TestProfileType.CC_CV,
        steps=[test_step_cc, test_step_cv, test_step_rest],
        default_cycles=3,
        safety_limits=SafetyLimits.default_battery_limits(),
        category="Test",
        tags=["test", "cc-cv"],
    )


@pytest.fixture
def sample_test(sample_test_profile):
    """Create a sample test."""
    test = Test(
        name="Test Battery Cycle",
        description="Test battery cycling",
        profile_id=sample_test_profile.id,
        steps=sample_test_profile.steps.copy(),
        total_cycles=sample_test_profile.default_cycles,
        safety_limits=sample_test_profile.safety_limits,
        tags=["test"],
    )
    return test


@pytest.fixture
def safety_limits_strict():
    """Create strict safety limits for testing."""
    return SafetyLimits(
        max_voltage=4.5,
        min_voltage=2.0,
        max_current=50.0,
        max_temperature=45.0,
        min_temperature=0.0,
        max_soc=95.0,
        min_soc=5.0,
        min_soh=70.0,
    )


@pytest.fixture
def safety_limits_permissive():
    """Create permissive safety limits for testing."""
    return SafetyLimits(
        max_voltage=6.0,
        min_voltage=-1.0,
        max_current=200.0,
        max_temperature=80.0,
        min_temperature=-40.0,
        max_soc=100.0,
        min_soc=0.0,
        min_soh=0.0,
    )


@pytest.fixture
def measurement_within_limits():
    """Create a measurement within safety limits."""
    return Measurement(
        id=uuid4(),
        timestamp=datetime.utcnow(),
        channel_id="test_channel_1",
        voltage=3.7,
        current=1.0,
        temperature=25.0,
        soc=50.0,
        soh=90.0,
    )


@pytest.fixture
def measurement_exceeding_limits():
    """Create a measurement exceeding safety limits."""
    return Measurement(
        id=uuid4(),
        timestamp=datetime.utcnow(),
        channel_id="test_channel_1",
        voltage=5.5,  # Exceeds typical max voltage
        current=150.0,  # Exceeds typical max current
        temperature=70.0,  # Exceeds typical max temperature
        soc=105.0,  # Invalid SOC
        soh=30.0,  # Low SOH
    )


# Async test helpers
@pytest.fixture
async def async_test_context():
    """Provide an async test context."""
    # Setup any async resources here
    yield
    # Cleanup any async resources here


# Mock data generators
@pytest.fixture
def measurement_generator():
    """Generator for creating test measurements."""
    def _generate(
        channel_id: str = "test_channel_1",
        voltage_range: tuple = (3.0, 4.2),
        current_range: tuple = (-10.0, 10.0),
        count: int = 100,
    ):
        import random
        measurements = []
        base_time = datetime.utcnow()
        
        for i in range(count):
            measurements.append(Measurement(
                id=uuid4(),
                timestamp=base_time.replace(second=i % 60, microsecond=i * 1000),
                channel_id=channel_id,
                voltage=random.uniform(*voltage_range),
                current=random.uniform(*current_range),
                temperature=random.uniform(20.0, 30.0),
                capacity=i * 0.01,
                energy=i * 0.037,
                step_number=1,
                cycle_number=1,
            ))
        
        return measurements
    
    return _generate


# Test markers
def pytest_configure(config):
    """Configure pytest markers."""
    config.addinivalue_line(
        "markers", "slow: marks tests as slow (deselect with '-m \"not slow\"')"
    )
    config.addinivalue_line(
        "markers", "integration: marks tests as integration tests"
    )
    config.addinivalue_line(
        "markers", "hardware: marks tests that require hardware"
    )
    config.addinivalue_line(
        "markers", "gui: marks tests that require GUI"
    )


# Test utilities
class TestUtils:
    """Utility functions for tests."""
    
    @staticmethod
    def assert_measurement_equal(m1: Measurement, m2: Measurement, tolerance: float = 1e-6):
        """Assert that two measurements are equal within tolerance."""
        assert m1.id == m2.id
        assert m1.channel_id == m2.channel_id
        assert abs((m1.timestamp - m2.timestamp).total_seconds()) < 1.0
        
        if m1.voltage is not None and m2.voltage is not None:
            assert abs(m1.voltage - m2.voltage) < tolerance
        
        if m1.current is not None and m2.current is not None:
            assert abs(m1.current - m2.current) < tolerance
        
        if m1.temperature is not None and m2.temperature is not None:
            assert abs(m1.temperature - m2.temperature) < tolerance
    
    @staticmethod
    def create_test_data_directory(base_path: Path) -> Path:
        """Create a test data directory structure."""
        data_dir = base_path / "test_data"
        data_dir.mkdir(exist_ok=True)
        
        # Create subdirectories
        (data_dir / "exports").mkdir(exist_ok=True)
        (data_dir / "configs").mkdir(exist_ok=True)
        (data_dir / "logs").mkdir(exist_ok=True)
        
        return data_dir


@pytest.fixture
def test_utils():
    """Provide test utilities."""
    return TestUtils
