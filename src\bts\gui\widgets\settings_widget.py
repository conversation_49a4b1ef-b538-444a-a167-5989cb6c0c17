"""Settings configuration widget."""

import logging

try:
    from PySide6.QtWidgets import (
        QWidget, QVBoxLayout, QHBoxLayout, QLabel,
        QPushButton, QGroupBox, QLineEdit, QSpinBox,
        QDoubleSpinBox, QCheckBox, QComboBox, QTextEdit,
        QFormLayout, QMessageBox, QTabWidget
    )
    from PySide6.QtCore import Qt
    PYSIDE6_AVAILABLE = True
except ImportError:
    PYSIDE6_AVAILABLE = False

if PYSIDE6_AVAILABLE:
    class SettingsWidget(QWidget):
        """Widget for system settings configuration."""
        
        def __init__(self, settings=None, parent=None):
            super().__init__(parent)
            
            self.logger = logging.getLogger(__name__)
            self.settings = settings
            
            self.setup_ui()
        
        def setup_ui(self):
            """Setup the settings UI."""
            layout = QVBoxLayout(self)
            
            # Title
            title = QLabel("System Settings")
            title.setStyleSheet("font-size: 16px; font-weight: bold;")
            layout.addWidget(title)
            
            # Settings tabs
            self.create_settings_tabs(layout)
            
            # Control buttons
            buttons_layout = QHBoxLayout()
            
            save_btn = QPushButton("💾 Save Settings")
            save_btn.clicked.connect(self.save_settings)
            buttons_layout.addWidget(save_btn)
            
            reset_btn = QPushButton("🔄 Reset to Defaults")
            reset_btn.clicked.connect(self.reset_settings)
            buttons_layout.addWidget(reset_btn)
            
            buttons_layout.addStretch()
            layout.addLayout(buttons_layout)
        
        def create_settings_tabs(self, parent_layout):
            """Create settings tabs."""
            tab_widget = QTabWidget()
            
            # System settings tab
            self.create_system_tab(tab_widget)
            
            # Safety settings tab
            self.create_safety_tab(tab_widget)
            
            # Hardware settings tab
            self.create_hardware_tab(tab_widget)
            
            # GUI settings tab
            self.create_gui_tab(tab_widget)
            
            parent_layout.addWidget(tab_widget)
        
        def create_system_tab(self, tab_widget):
            """Create system settings tab."""
            widget = QWidget()
            layout = QVBoxLayout(widget)
            
            # System group
            system_group = QGroupBox("System Configuration")
            form_layout = QFormLayout(system_group)
            
            self.system_name_edit = QLineEdit()
            if self.settings:
                self.system_name_edit.setText(self.settings.name)
            form_layout.addRow("System Name:", self.system_name_edit)
            
            self.max_channels_spin = QSpinBox()
            self.max_channels_spin.setRange(1, 10000)
            if self.settings:
                self.max_channels_spin.setValue(self.settings.max_channels)
            form_layout.addRow("Max Channels:", self.max_channels_spin)
            
            self.sampling_rate_spin = QDoubleSpinBox()
            self.sampling_rate_spin.setRange(0.1, 1000.0)
            self.sampling_rate_spin.setSuffix(" Hz")
            if self.settings:
                self.sampling_rate_spin.setValue(self.settings.sampling_rate_hz)
            form_layout.addRow("Sampling Rate:", self.sampling_rate_spin)
            
            layout.addWidget(system_group)
            layout.addStretch()
            
            tab_widget.addTab(widget, "System")
        
        def create_safety_tab(self, tab_widget):
            """Create safety settings tab."""
            widget = QWidget()
            layout = QVBoxLayout(widget)
            
            # Safety limits group
            safety_group = QGroupBox("Safety Limits")
            form_layout = QFormLayout(safety_group)
            
            self.voltage_limit_spin = QDoubleSpinBox()
            self.voltage_limit_spin.setRange(0.0, 100.0)
            self.voltage_limit_spin.setSuffix(" V")
            if self.settings:
                self.voltage_limit_spin.setValue(self.settings.safety.global_voltage_limit)
            form_layout.addRow("Global Voltage Limit:", self.voltage_limit_spin)
            
            self.current_limit_spin = QDoubleSpinBox()
            self.current_limit_spin.setRange(0.0, 1000.0)
            self.current_limit_spin.setSuffix(" A")
            if self.settings:
                self.current_limit_spin.setValue(self.settings.safety.global_current_limit)
            form_layout.addRow("Global Current Limit:", self.current_limit_spin)
            
            self.temp_limit_spin = QDoubleSpinBox()
            self.temp_limit_spin.setRange(-50.0, 200.0)
            self.temp_limit_spin.setSuffix(" °C")
            if self.settings:
                self.temp_limit_spin.setValue(self.settings.safety.temperature_limit)
            form_layout.addRow("Temperature Limit:", self.temp_limit_spin)
            
            self.emergency_stop_check = QCheckBox()
            if self.settings:
                self.emergency_stop_check.setChecked(self.settings.safety.emergency_stop_enabled)
            form_layout.addRow("Emergency Stop Enabled:", self.emergency_stop_check)
            
            layout.addWidget(safety_group)
            layout.addStretch()
            
            tab_widget.addTab(widget, "Safety")
        
        def create_hardware_tab(self, tab_widget):
            """Create hardware settings tab."""
            widget = QWidget()
            layout = QVBoxLayout(widget)
            
            # Hardware group
            hardware_group = QGroupBox("Hardware Configuration")
            form_layout = QFormLayout(hardware_group)
            
            self.auto_discovery_check = QCheckBox()
            if self.settings:
                self.auto_discovery_check.setChecked(self.settings.hardware.auto_discovery)
            form_layout.addRow("Auto Discovery:", self.auto_discovery_check)
            
            self.connection_timeout_spin = QSpinBox()
            self.connection_timeout_spin.setRange(1, 300)
            self.connection_timeout_spin.setSuffix(" s")
            if self.settings:
                self.connection_timeout_spin.setValue(self.settings.hardware.connection_timeout)
            form_layout.addRow("Connection Timeout:", self.connection_timeout_spin)
            
            self.retry_attempts_spin = QSpinBox()
            self.retry_attempts_spin.setRange(1, 10)
            if self.settings:
                self.retry_attempts_spin.setValue(self.settings.hardware.retry_attempts)
            form_layout.addRow("Retry Attempts:", self.retry_attempts_spin)
            
            layout.addWidget(hardware_group)
            layout.addStretch()
            
            tab_widget.addTab(widget, "Hardware")
        
        def create_gui_tab(self, tab_widget):
            """Create GUI settings tab."""
            widget = QWidget()
            layout = QVBoxLayout(widget)
            
            # GUI group
            gui_group = QGroupBox("GUI Configuration")
            form_layout = QFormLayout(gui_group)
            
            self.theme_combo = QComboBox()
            self.theme_combo.addItems(["dark", "light"])
            if self.settings:
                index = self.theme_combo.findText(self.settings.gui.theme)
                if index >= 0:
                    self.theme_combo.setCurrentIndex(index)
            form_layout.addRow("Theme:", self.theme_combo)
            
            self.language_combo = QComboBox()
            self.language_combo.addItems(["en", "es", "fr", "de", "zh"])
            if self.settings:
                index = self.language_combo.findText(self.settings.gui.language)
                if index >= 0:
                    self.language_combo.setCurrentIndex(index)
            form_layout.addRow("Language:", self.language_combo)
            
            self.update_interval_spin = QSpinBox()
            self.update_interval_spin.setRange(50, 5000)
            self.update_interval_spin.setSuffix(" ms")
            if self.settings:
                self.update_interval_spin.setValue(self.settings.gui.update_interval)
            form_layout.addRow("Update Interval:", self.update_interval_spin)
            
            layout.addWidget(gui_group)
            layout.addStretch()
            
            tab_widget.addTab(widget, "GUI")
        
        def save_settings(self):
            """Save current settings."""
            try:
                if not self.settings:
                    QMessageBox.warning(self, "Save Settings", "No settings object available")
                    return
                
                # Update settings from UI
                self.settings.name = self.system_name_edit.text()
                self.settings.max_channels = self.max_channels_spin.value()
                self.settings.sampling_rate_hz = self.sampling_rate_spin.value()
                
                self.settings.safety.global_voltage_limit = self.voltage_limit_spin.value()
                self.settings.safety.global_current_limit = self.current_limit_spin.value()
                self.settings.safety.temperature_limit = self.temp_limit_spin.value()
                self.settings.safety.emergency_stop_enabled = self.emergency_stop_check.isChecked()
                
                self.settings.hardware.auto_discovery = self.auto_discovery_check.isChecked()
                self.settings.hardware.connection_timeout = self.connection_timeout_spin.value()
                self.settings.hardware.retry_attempts = self.retry_attempts_spin.value()
                
                self.settings.gui.theme = self.theme_combo.currentText()
                self.settings.gui.language = self.language_combo.currentText()
                self.settings.gui.update_interval = self.update_interval_spin.value()
                
                # Save to file
                if self.settings.save_to_file("bts.toml"):
                    QMessageBox.information(self, "Save Settings", "Settings saved successfully!")
                else:
                    QMessageBox.warning(self, "Save Settings", "Failed to save settings to file")
                
            except Exception as e:
                self.logger.error(f"Error saving settings: {e}")
                QMessageBox.critical(self, "Save Settings", f"Error saving settings:\n{e}")
        
        def reset_settings(self):
            """Reset settings to defaults."""
            reply = QMessageBox.question(
                self,
                "Reset Settings",
                "Are you sure you want to reset all settings to defaults?",
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.No
            )
            
            if reply == QMessageBox.Yes:
                QMessageBox.information(self, "Reset Settings", "Reset to defaults not yet implemented")

else:
    class SettingsWidget:
        def __init__(self, *args, **kwargs):
            raise ImportError("PySide6 is required for GUI functionality")
