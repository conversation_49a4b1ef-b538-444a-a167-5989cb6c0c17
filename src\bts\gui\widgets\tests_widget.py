"""Tests management widget."""

import logging

try:
    from PySide6.QtWidgets import (
        QWidget, QVBoxLayout, QHBoxLayout, QLabel,
        QPushButton, QTableWidget, QTableWidgetItem,
        QGroupBox, QMessageBox, QProgressBar
    )
    from PySide6.QtCore import Qt, QTimer
    PYSIDE6_AVAILABLE = True
except ImportError:
    PYSIDE6_AVAILABLE = False

if PYSIDE6_AVAILABLE:
    class TestsWidget(QWidget):
        """Widget for managing tests."""
        
        def __init__(self, test_engine=None, hardware_manager=None, parent=None):
            super().__init__(parent)
            
            self.logger = logging.getLogger(__name__)
            self.test_engine = test_engine
            self.hardware_manager = hardware_manager
            
            self.tests_table = None
            self.setup_ui()
            
            # Update timer
            self.update_timer = QTimer()
            self.update_timer.timeout.connect(self.update_tests)
            self.update_timer.start(2000)  # Update every 2 seconds
        
        def setup_ui(self):
            """Setup the tests UI."""
            layout = QVBoxLayout(self)
            
            # Title
            title = QLabel("Test Management")
            title.setStyleSheet("font-size: 16px; font-weight: bold;")
            layout.addWidget(title)
            
            # Control buttons
            buttons_layout = QHBoxLayout()
            
            new_test_btn = QPushButton("🧪 New Test")
            new_test_btn.clicked.connect(self.new_test)
            buttons_layout.addWidget(new_test_btn)
            
            start_btn = QPushButton("▶️ Start")
            start_btn.clicked.connect(self.start_test)
            buttons_layout.addWidget(start_btn)
            
            pause_btn = QPushButton("⏸️ Pause")
            pause_btn.clicked.connect(self.pause_test)
            buttons_layout.addWidget(pause_btn)
            
            stop_btn = QPushButton("⏹️ Stop")
            stop_btn.clicked.connect(self.stop_test)
            buttons_layout.addWidget(stop_btn)
            
            buttons_layout.addStretch()
            layout.addLayout(buttons_layout)
            
            # Tests table
            self.create_tests_table(layout)
        
        def create_tests_table(self, parent_layout):
            """Create the tests table."""
            group = QGroupBox("Active Tests")
            layout = QVBoxLayout(group)
            
            self.tests_table = QTableWidget()
            self.tests_table.setColumnCount(7)
            self.tests_table.setHorizontalHeaderLabels([
                "Name", "Status", "Progress", "Channel", "Step", "Cycle", "Duration"
            ])
            
            layout.addWidget(self.tests_table)
            parent_layout.addWidget(group)
            
            self.update_tests()
        
        def update_tests(self):
            """Update the tests table."""
            if not self.test_engine:
                return
            
            try:
                active_tests = self.test_engine.active_tests
                self.tests_table.setRowCount(len(active_tests))
                
                for row, test in enumerate(active_tests):
                    # Name
                    self.tests_table.setItem(row, 0, QTableWidgetItem(test.name))
                    
                    # Status
                    status_colors = {
                        "running": "🟢",
                        "paused": "🟡", 
                        "stopped": "🔴",
                        "completed": "✅",
                        "error": "❌"
                    }
                    status_icon = status_colors.get(test.state.value, "❓")
                    status_text = f"{status_icon} {test.state.value.upper()}"
                    self.tests_table.setItem(row, 1, QTableWidgetItem(status_text))
                    
                    # Progress
                    progress_item = QTableWidgetItem()
                    if hasattr(test, 'progress_percentage'):
                        progress_item.setText(f"{test.progress_percentage:.1f}%")
                    else:
                        progress_item.setText("0%")
                    self.tests_table.setItem(row, 2, progress_item)
                    
                    # Channel
                    channel_text = test.channel_id if test.channel_id else "None"
                    self.tests_table.setItem(row, 3, QTableWidgetItem(channel_text))
                    
                    # Step
                    step_text = str(test.current_step) if test.current_step else "0"
                    self.tests_table.setItem(row, 4, QTableWidgetItem(step_text))
                    
                    # Cycle
                    cycle_text = f"{test.current_cycle}/{test.total_cycles}"
                    self.tests_table.setItem(row, 5, QTableWidgetItem(cycle_text))
                    
                    # Duration
                    duration_text = f"{test.duration:.0f}s" if test.duration else "0s"
                    self.tests_table.setItem(row, 6, QTableWidgetItem(duration_text))
                
            except Exception as e:
                self.logger.error(f"Error updating tests table: {e}")
        
        def new_test(self):
            """Create a new test."""
            QMessageBox.information(self, "New Test", "New test dialog not yet implemented")
        
        def start_test(self):
            """Start selected test."""
            QMessageBox.information(self, "Start Test", "Start test functionality not yet implemented")
        
        def pause_test(self):
            """Pause selected test."""
            QMessageBox.information(self, "Pause Test", "Pause test functionality not yet implemented")
        
        def stop_test(self):
            """Stop selected test."""
            QMessageBox.information(self, "Stop Test", "Stop test functionality not yet implemented")

else:
    class TestsWidget:
        def __init__(self, *args, **kwargs):
            raise ImportError("PySide6 is required for GUI functionality")
