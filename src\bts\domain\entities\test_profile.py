"""Test profile entity for battery testing templates."""

from dataclasses import dataclass, field
from datetime import datetime
from enum import Enum
from typing import Dict, Any, Optional, List
from uuid import UUID, uuid4

from .test import TestStep
from ..value_objects.safety_limits import SafetyLimits


class TestProfileType(Enum):
    """Types of test profiles."""
    CC_CV = "cc_cv"  # Constant Current - Constant Voltage
    CP = "constant_power"  # Constant Power
    EIS = "eis"  # Electrochemical Impedance Spectroscopy
    PULSE = "pulse"  # Pulse testing
    DRIVE_CYCLE = "drive_cycle"  # Drive cycle replay
    CALENDAR_AGING = "calendar_aging"  # Calendar aging
    CYCLE_AGING = "cycle_aging"  # Cycle aging
    CUSTOM = "custom"  # Custom profile


@dataclass
class TestProfile:
    """
    Represents a reusable test profile template.
    
    Test profiles define the sequence of steps and parameters
    that can be applied to create actual tests.
    """
    
    # Core identification
    id: UUID = field(default_factory=uuid4)
    name: str = ""
    description: Optional[str] = None
    profile_type: TestProfileType = TestProfileType.CUSTOM
    
    # Profile configuration
    steps: List[TestStep] = field(default_factory=list)
    default_cycles: int = 1
    
    # Safety and limits
    safety_limits: Optional[SafetyLimits] = None
    
    # Metadata
    version: str = "1.0"
    author: Optional[str] = None
    created_at: datetime = field(default_factory=datetime.utcnow)
    updated_at: datetime = field(default_factory=datetime.utcnow)
    
    # Classification
    tags: List[str] = field(default_factory=list)
    category: Optional[str] = None
    
    # Usage tracking
    usage_count: int = 0
    last_used: Optional[datetime] = None
    
    # Validation and requirements
    min_voltage: Optional[float] = None
    max_voltage: Optional[float] = None
    min_capacity: Optional[float] = None
    max_capacity: Optional[float] = None
    
    # Additional metadata
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    def __post_init__(self):
        """Validate test profile after initialization."""
        if not self.name:
            self.name = f"Profile_{self.id.hex[:8]}"
        
        if self.default_cycles < 1:
            raise ValueError("Default cycles must be at least 1")
        
        # Validate step numbering
        for i, step in enumerate(self.steps):
            if step.step_number != i + 1:
                step.step_number = i + 1
    
    @property
    def is_charge_profile(self) -> bool:
        """Check if this is primarily a charging profile."""
        charge_steps = sum(1 for step in self.steps if step.is_charge_step)
        return charge_steps > len(self.steps) / 2
    
    @property
    def is_discharge_profile(self) -> bool:
        """Check if this is primarily a discharging profile."""
        discharge_steps = sum(1 for step in self.steps if step.is_discharge_step)
        return discharge_steps > len(self.steps) / 2
    
    @property
    def estimated_duration(self) -> Optional[float]:
        """Estimate total duration in seconds for one cycle."""
        if not self.steps:
            return None
        
        total_duration = 0.0
        for step in self.steps:
            if step.duration:
                total_duration += step.duration
            else:
                # If no duration specified, we can't estimate
                return None
        
        return total_duration
    
    @property
    def step_count(self) -> int:
        """Get the number of steps in this profile."""
        return len(self.steps)
    
    def add_step(self, step: TestStep) -> None:
        """Add a step to the profile."""
        step.step_number = len(self.steps) + 1
        self.steps.append(step)
        self._update_timestamp()
    
    def insert_step(self, index: int, step: TestStep) -> None:
        """Insert a step at a specific position."""
        if index < 0 or index > len(self.steps):
            raise ValueError("Invalid step index")
        
        self.steps.insert(index, step)
        # Renumber all steps
        for i, s in enumerate(self.steps):
            s.step_number = i + 1
        self._update_timestamp()
    
    def remove_step(self, step_number: int) -> None:
        """Remove a step by step number."""
        self.steps = [s for s in self.steps if s.step_number != step_number]
        # Renumber remaining steps
        for i, step in enumerate(self.steps):
            step.step_number = i + 1
        self._update_timestamp()
    
    def move_step(self, from_index: int, to_index: int) -> None:
        """Move a step from one position to another."""
        if (from_index < 0 or from_index >= len(self.steps) or
            to_index < 0 or to_index >= len(self.steps)):
            raise ValueError("Invalid step indices")
        
        step = self.steps.pop(from_index)
        self.steps.insert(to_index, step)
        # Renumber all steps
        for i, s in enumerate(self.steps):
            s.step_number = i + 1
        self._update_timestamp()
    
    def get_step(self, step_number: int) -> Optional[TestStep]:
        """Get a step by step number."""
        for step in self.steps:
            if step.step_number == step_number:
                return step
        return None
    
    def clone(self, new_name: Optional[str] = None) -> "TestProfile":
        """Create a copy of this profile with a new ID."""
        cloned = TestProfile(
            name=new_name or f"{self.name}_copy",
            description=self.description,
            profile_type=self.profile_type,
            steps=[
                TestStep(
                    step_number=step.step_number,
                    name=step.name,
                    step_type=step.step_type,
                    target_value=step.target_value,
                    limit_value=step.limit_value,
                    limit_type=step.limit_type,
                    duration=step.duration,
                    end_conditions=step.end_conditions.copy(),
                    description=step.description,
                    metadata=step.metadata.copy(),
                )
                for step in self.steps
            ],
            default_cycles=self.default_cycles,
            safety_limits=self.safety_limits,
            version="1.0",
            author=self.author,
            tags=self.tags.copy(),
            category=self.category,
            min_voltage=self.min_voltage,
            max_voltage=self.max_voltage,
            min_capacity=self.min_capacity,
            max_capacity=self.max_capacity,
            metadata=self.metadata.copy(),
        )
        return cloned
    
    def validate(self) -> List[str]:
        """Validate the profile and return list of issues."""
        issues = []
        
        if not self.steps:
            issues.append("Profile must have at least one step")
        
        # Check step numbering
        expected_numbers = list(range(1, len(self.steps) + 1))
        actual_numbers = [step.step_number for step in self.steps]
        if actual_numbers != expected_numbers:
            issues.append("Step numbers are not sequential")
        
        # Check for duplicate step names
        step_names = [step.name for step in self.steps]
        if len(step_names) != len(set(step_names)):
            issues.append("Duplicate step names found")
        
        # Validate voltage ranges
        if (self.min_voltage is not None and self.max_voltage is not None and
            self.min_voltage >= self.max_voltage):
            issues.append("Minimum voltage must be less than maximum voltage")
        
        # Validate capacity ranges
        if (self.min_capacity is not None and self.max_capacity is not None and
            self.min_capacity >= self.max_capacity):
            issues.append("Minimum capacity must be less than maximum capacity")
        
        return issues
    
    def mark_used(self) -> None:
        """Mark the profile as used (increment usage count)."""
        self.usage_count += 1
        self.last_used = datetime.utcnow()
        self._update_timestamp()
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert test profile to dictionary for serialization."""
        data = {
            "id": str(self.id),
            "name": self.name,
            "description": self.description,
            "profile_type": self.profile_type.value,
            "steps": [step.to_dict() for step in self.steps],
            "default_cycles": self.default_cycles,
            "version": self.version,
            "author": self.author,
            "created_at": self.created_at.isoformat(),
            "updated_at": self.updated_at.isoformat(),
            "tags": self.tags,
            "category": self.category,
            "usage_count": self.usage_count,
            "last_used": self.last_used.isoformat() if self.last_used else None,
            "min_voltage": self.min_voltage,
            "max_voltage": self.max_voltage,
            "min_capacity": self.min_capacity,
            "max_capacity": self.max_capacity,
            "metadata": self.metadata,
        }
        
        if self.safety_limits:
            data["safety_limits"] = self.safety_limits.to_dict()
        
        return data
    
    def _update_timestamp(self) -> None:
        """Update the last modified timestamp."""
        self.updated_at = datetime.utcnow()
    
    @classmethod
    def create_cc_cv_profile(
        cls,
        name: str,
        charge_current: float,
        charge_voltage: float,
        discharge_current: float,
        discharge_voltage: float,
        cycles: int = 1,
    ) -> "TestProfile":
        """Create a standard CC-CV charge/discharge profile."""
        from .test import TestStep, StepType
        
        steps = [
            TestStep(
                step_number=1,
                name="CC Charge",
                step_type=StepType.CC,
                target_value=charge_current,
                limit_value=charge_voltage,
                limit_type="voltage",
                description=f"Charge at {charge_current}A until {charge_voltage}V",
            ),
            TestStep(
                step_number=2,
                name="CV Charge",
                step_type=StepType.CV,
                target_value=charge_voltage,
                limit_value=charge_current * 0.05,  # C/20 termination
                limit_type="current",
                description=f"Hold {charge_voltage}V until current drops to {charge_current * 0.05}A",
            ),
            TestStep(
                step_number=3,
                name="Rest",
                step_type=StepType.REST,
                duration=300,  # 5 minutes
                description="Rest for 5 minutes",
            ),
            TestStep(
                step_number=4,
                name="CC Discharge",
                step_type=StepType.CC,
                target_value=-abs(discharge_current),
                limit_value=discharge_voltage,
                limit_type="voltage",
                description=f"Discharge at {discharge_current}A until {discharge_voltage}V",
            ),
            TestStep(
                step_number=5,
                name="Rest",
                step_type=StepType.REST,
                duration=300,  # 5 minutes
                description="Rest for 5 minutes",
            ),
        ]
        
        return cls(
            name=name,
            description=f"CC-CV charge/discharge profile: {charge_current}A/{charge_voltage}V charge, {discharge_current}A/{discharge_voltage}V discharge",
            profile_type=TestProfileType.CC_CV,
            steps=steps,
            default_cycles=cycles,
            category="Standard",
            tags=["cc-cv", "charge", "discharge"],
        )
