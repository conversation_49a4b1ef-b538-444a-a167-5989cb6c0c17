"""Test entity for battery testing operations."""

from dataclasses import dataclass, field
from datetime import datetime
from enum import Enum
from typing import Dict, Any, Optional, List
from uuid import UUID, uuid4

from ..value_objects.safety_limits import SafetyLimits


class TestState(Enum):
    """States that a test can be in."""
    CREATED = "created"
    QUEUED = "queued"
    RUNNING = "running"
    PAUSED = "paused"
    COMPLETED = "completed"
    STOPPED = "stopped"
    ERROR = "error"
    CANCELLED = "cancelled"


class StepType(Enum):
    """Types of test steps."""
    CC = "constant_current"  # Constant Current
    CV = "constant_voltage"  # Constant Voltage
    CP = "constant_power"    # Constant Power
    CR = "constant_resistance"  # Constant Resistance
    REST = "rest"           # Rest/Open Circuit
    PULSE = "pulse"         # Pulse test
    EIS = "eis"            # Electrochemical Impedance Spectroscopy
    CUSTOM = "custom"       # Custom step type


@dataclass
class TestStep:
    """
    Represents a single step in a test profile.
    """
    
    # Step identification
    step_number: int
    name: str
    step_type: StepType
    
    # Step parameters
    target_value: Optional[float] = None  # Target current, voltage, power, etc.
    limit_value: Optional[float] = None   # Limit value for termination
    limit_type: Optional[str] = None      # Type of limit (voltage, time, capacity, etc.)
    
    # Timing
    duration: Optional[float] = None      # Maximum duration in seconds
    
    # Conditions
    end_conditions: List[Dict[str, Any]] = field(default_factory=list)
    
    # Metadata
    description: Optional[str] = None
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    def __post_init__(self):
        """Validate test step after initialization."""
        if self.step_number < 1:
            raise ValueError("Step number must be positive")
        
        if not self.name:
            raise ValueError("Step name cannot be empty")
        
        if self.duration is not None and self.duration <= 0:
            raise ValueError("Duration must be positive")
    
    @property
    def is_charge_step(self) -> bool:
        """Check if this is a charging step."""
        return (self.step_type in [StepType.CC, StepType.CV, StepType.CP] and
                self.target_value is not None and self.target_value > 0)
    
    @property
    def is_discharge_step(self) -> bool:
        """Check if this is a discharging step."""
        return (self.step_type in [StepType.CC, StepType.CP] and
                self.target_value is not None and self.target_value < 0)
    
    @property
    def is_rest_step(self) -> bool:
        """Check if this is a rest step."""
        return self.step_type == StepType.REST
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert test step to dictionary."""
        return {
            "step_number": self.step_number,
            "name": self.name,
            "step_type": self.step_type.value,
            "target_value": self.target_value,
            "limit_value": self.limit_value,
            "limit_type": self.limit_type,
            "duration": self.duration,
            "end_conditions": self.end_conditions,
            "description": self.description,
            "metadata": self.metadata,
        }


@dataclass
class Test:
    """
    Represents a complete battery test with profile and execution state.
    """
    
    # Core identification
    id: UUID = field(default_factory=uuid4)
    name: str = ""
    description: Optional[str] = None
    
    # Test configuration
    profile_id: Optional[UUID] = None
    channel_id: Optional[str] = None
    steps: List[TestStep] = field(default_factory=list)
    
    # State management
    state: TestState = TestState.CREATED
    created_at: datetime = field(default_factory=datetime.utcnow)
    started_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None
    updated_at: datetime = field(default_factory=datetime.utcnow)
    
    # Execution state
    current_step: Optional[int] = None
    current_cycle: int = 1
    total_cycles: int = 1
    step_start_time: Optional[datetime] = None
    
    # Safety and limits
    safety_limits: Optional[SafetyLimits] = None
    
    # Results and statistics
    total_capacity: Optional[float] = None  # Total capacity (Ah)
    total_energy: Optional[float] = None    # Total energy (Wh)
    peak_power: Optional[float] = None      # Peak power (W)
    average_efficiency: Optional[float] = None  # Average efficiency (%)
    
    # Status information
    error_message: Optional[str] = None
    warning_messages: List[str] = field(default_factory=list)
    
    # Metadata
    tags: List[str] = field(default_factory=list)
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    def __post_init__(self):
        """Validate test data after initialization."""
        if not self.name:
            self.name = f"Test_{self.id.hex[:8]}"
        
        if self.total_cycles < 1:
            raise ValueError("Total cycles must be at least 1")
        
        if self.current_cycle < 1:
            raise ValueError("Current cycle must be at least 1")
    
    @property
    def is_running(self) -> bool:
        """Check if test is currently running."""
        return self.state == TestState.RUNNING
    
    @property
    def is_paused(self) -> bool:
        """Check if test is paused."""
        return self.state == TestState.PAUSED
    
    @property
    def is_completed(self) -> bool:
        """Check if test is completed."""
        return self.state == TestState.COMPLETED
    
    @property
    def has_error(self) -> bool:
        """Check if test has an error."""
        return self.state == TestState.ERROR
    
    @property
    def can_start(self) -> bool:
        """Check if test can be started."""
        return self.state in [TestState.CREATED, TestState.QUEUED]
    
    @property
    def can_pause(self) -> bool:
        """Check if test can be paused."""
        return self.state == TestState.RUNNING
    
    @property
    def can_resume(self) -> bool:
        """Check if test can be resumed."""
        return self.state == TestState.PAUSED
    
    @property
    def can_stop(self) -> bool:
        """Check if test can be stopped."""
        return self.state in [TestState.RUNNING, TestState.PAUSED]
    
    @property
    def duration(self) -> Optional[float]:
        """Get test duration in seconds."""
        if not self.started_at:
            return None
        
        end_time = self.completed_at or datetime.utcnow()
        return (end_time - self.started_at).total_seconds()
    
    @property
    def progress_percentage(self) -> float:
        """Get test progress as percentage."""
        if not self.steps:
            return 0.0
        
        if self.current_step is None:
            return 0.0
        
        if self.is_completed:
            return 100.0
        
        # Calculate progress based on current step and cycle
        steps_per_cycle = len(self.steps)
        completed_steps = (self.current_cycle - 1) * steps_per_cycle + (self.current_step - 1)
        total_steps = self.total_cycles * steps_per_cycle
        
        return min(100.0, (completed_steps / total_steps) * 100.0)

    def start(self, channel_id: str) -> None:
        """Start the test on a specific channel."""
        if not self.can_start:
            raise ValueError(f"Test {self.id} cannot be started in state {self.state}")

        self.channel_id = channel_id
        self.state = TestState.RUNNING
        self.started_at = datetime.utcnow()
        self.current_step = 1 if self.steps else None
        self.current_cycle = 1
        self.step_start_time = datetime.utcnow()
        self.error_message = None
        self.warning_messages.clear()
        self._update_timestamp()

    def pause(self) -> None:
        """Pause the test."""
        if not self.can_pause:
            raise ValueError(f"Test {self.id} cannot be paused in state {self.state}")

        self.state = TestState.PAUSED
        self._update_timestamp()

    def resume(self) -> None:
        """Resume a paused test."""
        if not self.can_resume:
            raise ValueError(f"Test {self.id} cannot be resumed in state {self.state}")

        self.state = TestState.RUNNING
        self._update_timestamp()

    def stop(self) -> None:
        """Stop the test."""
        if not self.can_stop:
            raise ValueError(f"Test {self.id} cannot be stopped in state {self.state}")

        self.state = TestState.STOPPED
        self.completed_at = datetime.utcnow()
        self._update_timestamp()

    def complete(self) -> None:
        """Mark the test as completed."""
        if not self.is_running:
            raise ValueError(f"Test {self.id} is not running")

        self.state = TestState.COMPLETED
        self.completed_at = datetime.utcnow()
        self._update_timestamp()

    def set_error(self, error_message: str) -> None:
        """Set test to error state with message."""
        self.state = TestState.ERROR
        self.error_message = error_message
        self.completed_at = datetime.utcnow()
        self._update_timestamp()

    def add_warning(self, warning_message: str) -> None:
        """Add a warning message to the test."""
        self.warning_messages.append(warning_message)
        self._update_timestamp()

    def advance_step(self) -> bool:
        """Advance to the next step. Returns True if advanced, False if test completed."""
        if not self.is_running:
            raise ValueError(f"Test {self.id} is not running")

        if not self.steps or self.current_step is None:
            return False

        # Check if we're at the last step of the current cycle
        if self.current_step >= len(self.steps):
            # Move to next cycle
            if self.current_cycle >= self.total_cycles:
                # Test completed
                self.complete()
                return False
            else:
                # Start next cycle
                self.current_cycle += 1
                self.current_step = 1
        else:
            # Move to next step in current cycle
            self.current_step += 1

        self.step_start_time = datetime.utcnow()
        self._update_timestamp()
        return True

    def get_current_step(self) -> Optional[TestStep]:
        """Get the current test step."""
        if not self.steps or self.current_step is None:
            return None

        if 1 <= self.current_step <= len(self.steps):
            return self.steps[self.current_step - 1]

        return None

    def add_step(self, step: TestStep) -> None:
        """Add a step to the test."""
        if self.is_running:
            raise ValueError("Cannot modify steps while test is running")

        self.steps.append(step)
        self._update_timestamp()

    def remove_step(self, step_number: int) -> None:
        """Remove a step from the test."""
        if self.is_running:
            raise ValueError("Cannot modify steps while test is running")

        self.steps = [s for s in self.steps if s.step_number != step_number]
        # Renumber remaining steps
        for i, step in enumerate(self.steps):
            step.step_number = i + 1
        self._update_timestamp()

    def to_dict(self) -> Dict[str, Any]:
        """Convert test to dictionary for serialization."""
        data = {
            "id": str(self.id),
            "name": self.name,
            "description": self.description,
            "profile_id": str(self.profile_id) if self.profile_id else None,
            "channel_id": self.channel_id,
            "state": self.state.value,
            "created_at": self.created_at.isoformat(),
            "started_at": self.started_at.isoformat() if self.started_at else None,
            "completed_at": self.completed_at.isoformat() if self.completed_at else None,
            "updated_at": self.updated_at.isoformat(),
            "current_step": self.current_step,
            "current_cycle": self.current_cycle,
            "total_cycles": self.total_cycles,
            "step_start_time": self.step_start_time.isoformat() if self.step_start_time else None,
            "steps": [step.to_dict() for step in self.steps],
            "total_capacity": self.total_capacity,
            "total_energy": self.total_energy,
            "peak_power": self.peak_power,
            "average_efficiency": self.average_efficiency,
            "error_message": self.error_message,
            "warning_messages": self.warning_messages,
            "tags": self.tags,
            "metadata": self.metadata,
        }

        if self.safety_limits:
            data["safety_limits"] = self.safety_limits.to_dict()

        return data

    def _update_timestamp(self) -> None:
        """Update the last modified timestamp."""
        self.updated_at = datetime.utcnow()
