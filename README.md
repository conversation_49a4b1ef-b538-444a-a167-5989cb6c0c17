# Battery Testing System (BTS)

A modular, extensible battery testing software for laboratories, EV pack manufacturers, and cell validation lines.

[![Python 3.9+](https://img.shields.io/badge/python-3.9+-blue.svg)](https://www.python.org/downloads/)
[![License: MIT](https://img.shields.io/badge/License-MIT-yellow.svg)](https://opensource.org/licenses/MIT)
[![Code style: black](https://img.shields.io/badge/code%20style-black-000000.svg)](https://github.com/psf/black)

## 🚀 Features

### Core Functionality
- **User-selectable test profiles**: CC-CV, CP, EIS, pulse, drive-cycle replay, calendar-aging
- **Unlimited channel support**: Each channel runs independent scripts
- **Real-time plotting**: Voltage, current, temperature, capacity, SoC, SOH with pause/resume & zoom
- **Safety interlocks**: Over-voltage, over-current, over-temperature, pressure, fire-alarm integration
- **Multi-database support**: SQLite, PostgreSQL, InfluxDB with auto-export to CSV/Excel/JSON
- **Report generation**: PDF/HTML reports with configurable templates and embedded plots
- **Role-based access control**: Admin/Operator/Viewer roles with audit trail
- **Scripting API**: Python API for custom experiments

### Hardware Support
- **Multiple protocols**: Modbus-TCP/RTU, CANbus, USB, Ethernet, serial
- **Cycler support**: Arbin, Bitrode, MACCOR, Neware, Chroma
- **Generic instruments**: Keithley SMU, NI-DAQmx, custom Arduino/Raspberry Pi
- **Hot-plug detection**: Automatic channel mapping
- **Hardware simulation**: Mock drivers for offline development

### Architecture
- **Clean Architecture**: Onion/Hexagonal architecture with clear separation of concerns
- **Async I/O**: Non-blocking communications with plug-in manager
- **Extensible**: Entry-point based plugin system for drivers and test profiles
- **Cross-platform**: Windows, Linux, macOS support

## 📋 Requirements

- Python 3.9 or higher
- Operating System: Windows 10+, Linux (Ubuntu 20.04+), macOS 10.15+
- Memory: 4GB RAM minimum, 8GB recommended
- Storage: 1GB free space for installation, additional space for data

### Optional Dependencies
- **GUI**: PySide6 for desktop interface
- **Web**: FastAPI + React for web interface
- **Hardware**: Specific drivers for your instruments
- **Database**: PostgreSQL or InfluxDB for production deployments

## 🛠️ Installation

### Quick Start (Recommended)

```bash
# Install from PyPI (when available)
pip install battery-testing-system

# Or install from source
git clone https://github.com/bts-team/battery-testing-system.git
cd battery-testing-system
pip install -e .
```

### Development Installation

```bash
# Clone repository
git clone https://github.com/bts-team/battery-testing-system.git
cd battery-testing-system

# Create virtual environment
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate

# Install development dependencies
pip install -e ".[dev,test,docs]"

# Initialize database and configuration
bts init
```

### Docker Installation

```bash
# Pull and run with Docker
docker pull bts/battery-testing-system
docker run -p 8000:8000 -v $(pwd)/data:/app/data bts/battery-testing-system
```

## 🚀 Quick Start Guide

### 1. Initialize the System

```bash
# Initialize database and create default configuration
bts init

# Check system status
bts status
```

### 2. Start the GUI Application

```bash
# Launch desktop GUI
bts gui

# Or start web server
bts serve --host 0.0.0.0 --port 8000
```

### 3. Add Your First Instrument

```python
from bts import Settings, HardwareManager, Instrument, InstrumentType

# Create instrument configuration
instrument = Instrument(
    id="my_cycler_1",
    name="Lab Cycler #1",
    instrument_type=InstrumentType.CYCLER,
    manufacturer="Arbin",
    model="BT2000",
    connection_string="*************:502",  # Modbus TCP
    driver_name="arbin",
    max_channels=8,
)

# Add to system (via GUI or programmatically)
```

### 4. Create a Test Profile

```python
from bts.domain.entities.test_profile import TestProfile
from bts.domain.entities.test import TestStep, StepType

# Create CC-CV profile
profile = TestProfile.create_cc_cv_profile(
    name="Standard Li-ion Cycle",
    charge_current=1.0,      # 1A charge
    charge_voltage=4.2,      # 4.2V max
    discharge_current=2.0,   # 2A discharge  
    discharge_voltage=2.5,   # 2.5V cutoff
    cycles=100
)
```

### 5. Run Your First Test

```python
from bts.domain.entities.test import Test

# Create test from profile
test = Test(
    name="Cell Validation Test",
    profile_id=profile.id,
    steps=profile.steps,
    total_cycles=profile.default_cycles,
)

# Start test (via GUI or API)
```

## 📖 Documentation

### User Guides
- [Installation Guide](docs/user/installation.md)
- [Getting Started](docs/user/getting_started.md)
- [Test Profile Creation](docs/user/test_profiles.md)
- [Hardware Configuration](docs/user/hardware_setup.md)
- [Data Export & Reports](docs/user/data_export.md)

### Developer Documentation
- [Architecture Overview](docs/developer/architecture.md)
- [API Reference](docs/api/index.md)
- [Plugin Development](docs/developer/plugins.md)
- [Contributing Guide](docs/developer/contributing.md)

### Hardware Integration
- [Supported Instruments](docs/hardware/supported_instruments.md)
- [Driver Development](docs/hardware/driver_development.md)
- [Protocol Documentation](docs/hardware/protocols.md)

## 🧪 Testing

```bash
# Run all tests
pytest

# Run specific test categories
pytest -m "not slow"           # Skip slow tests
pytest -m integration          # Integration tests only
pytest tests/unit/             # Unit tests only

# Run with coverage
pytest --cov=bts --cov-report=html
```

## 🔧 Configuration

BTS uses TOML configuration files with hierarchical settings:

```toml
# bts.toml
[system]
name = "Battery Testing System"
max_channels = 1000
sampling_rate_hz = 1.0

[database]
primary = "sqlite:///data/bts.db"
timeseries = "influxdb://localhost:8086/bts"

[safety]
global_voltage_limit = 5.0
global_current_limit = 100.0
temperature_limit = 60.0

[hardware]
auto_discovery = true
connection_timeout = 30

[gui]
theme = "dark"
update_interval = 100
```

## 🔌 Hardware Drivers

### Supported Instruments

| Manufacturer | Models | Protocol | Status |
|--------------|--------|----------|--------|
| Arbin | BT2000, LBT, MSTAT | Modbus TCP | ✅ Supported |
| Bitrode | FTF, MCV | Ethernet | 🚧 In Progress |
| MACCOR | Series 4000 | Serial/Ethernet | 🚧 In Progress |
| Neware | CT-4008T | Modbus RTU | 🚧 In Progress |
| Keithley | 2400 Series | USB/Ethernet | ✅ Supported |
| Mock | Simulator | Virtual | ✅ Supported |

### Adding New Drivers

```python
from bts.infrastructure.drivers.base import InstrumentDriver

class MyCustomDriver(InstrumentDriver):
    async def connect(self) -> bool:
        # Implement connection logic
        pass
    
    async def read_measurement(self, channel: int) -> Measurement:
        # Implement measurement reading
        pass
```

## 🔒 Safety Features

- **Real-time monitoring**: Continuous safety parameter checking
- **Configurable limits**: Per-channel and global safety limits
- **Emergency stop**: Immediate shutdown on safety violations
- **External integration**: Fire alarm, temperature chamber integration
- **Audit trail**: Complete logging of all safety events

## 📊 Data Management

### Supported Formats
- **Export**: CSV, Excel, JSON, HDF5
- **Databases**: SQLite, PostgreSQL, InfluxDB
- **Real-time**: WebSocket streaming, MQTT
- **Reports**: PDF, HTML with embedded plots

### Data Retention
- Configurable retention policies
- Automatic archival and compression
- Data integrity verification
- Backup and restore capabilities

## 🌐 Web Interface

Access BTS through your browser:

```bash
# Start web server
bts serve --host 0.0.0.0 --port 8000

# Open browser to http://localhost:8000
```

Features:
- Real-time dashboard
- Remote test monitoring
- Data visualization
- Mobile-responsive design

## 🤝 Contributing

We welcome contributions! Please see our [Contributing Guide](docs/developer/contributing.md) for details.

### Development Setup

```bash
# Fork and clone the repository
git clone https://github.com/yourusername/battery-testing-system.git
cd battery-testing-system

# Install development dependencies
pip install -e ".[dev]"

# Install pre-commit hooks
pre-commit install

# Run tests
pytest
```

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🆘 Support

- **Documentation**: [https://battery-testing-system.readthedocs.io/](https://battery-testing-system.readthedocs.io/)
- **Issues**: [GitHub Issues](https://github.com/bts-team/battery-testing-system/issues)
- **Discussions**: [GitHub Discussions](https://github.com/bts-team/battery-testing-system/discussions)
- **Email**: <EMAIL>

## 🙏 Acknowledgments

- Built with [PySide6](https://www.qt.io/qt-for-python) for GUI
- [FastAPI](https://fastapi.tiangolo.com/) for web interface
- [SQLAlchemy](https://www.sqlalchemy.org/) for database ORM
- [Pydantic](https://pydantic-docs.helpmanual.io/) for data validation
- [pytest](https://pytest.org/) for testing framework

## 🗺️ Roadmap

- [ ] Additional hardware driver support
- [ ] Advanced data analytics and ML integration
- [ ] Cloud deployment options
- [ ] Mobile application
- [ ] Advanced reporting templates
- [ ] Integration with LIMS systems

---

**Battery Testing System** - Empowering battery research and manufacturing with professional-grade testing software.
