"""Unit tests for domain entities."""

import pytest
from datetime import datetime, timedelta
from uuid import uuid4

from bts.domain.entities.measurement import Measurement, MeasurementType
from bts.domain.entities.channel import Channel, ChannelState
from bts.domain.entities.test import Test, TestState, TestStep, StepType
from bts.domain.entities.test_profile import TestProfile, TestProfileType
from bts.domain.entities.instrument import Instrument, InstrumentType, InstrumentState
from bts.domain.entities.user import User, UserRole
from bts.domain.value_objects.safety_limits import SafetyLimits


class TestMeasurement:
    """Test cases for Measurement entity."""
    
    def test_measurement_creation(self):
        """Test basic measurement creation."""
        measurement = Measurement(
            id=uuid4(),
            timestamp=datetime.utcnow(),
            channel_id="ch1",
            voltage=3.7,
            current=1.0,
            temperature=25.0,
        )
        
        assert measurement.voltage == 3.7
        assert measurement.current == 1.0
        assert measurement.temperature == 25.0
        assert measurement.has_primary_data
        assert measurement.has_temperature_data
        assert not measurement.has_derived_data
    
    def test_measurement_validation(self):
        """Test measurement validation."""
        # Test negative voltage validation
        with pytest.raises(ValueError, match="Voltage cannot be negative"):
            Measurement(
                id=uuid4(),
                timestamp=datetime.utcnow(),
                channel_id="ch1",
                voltage=-1.0,
            )
        
        # Test invalid SOC validation
        with pytest.raises(ValueError, match="SOC must be between 0 and 100"):
            Measurement(
                id=uuid4(),
                timestamp=datetime.utcnow(),
                channel_id="ch1",
                soc=150.0,
            )
    
    def test_measurement_get_value(self):
        """Test getting values by measurement type."""
        measurement = Measurement(
            id=uuid4(),
            timestamp=datetime.utcnow(),
            channel_id="ch1",
            voltage=3.7,
            current=1.0,
            temperature=25.0,
        )
        
        assert measurement.get_value(MeasurementType.VOLTAGE) == 3.7
        assert measurement.get_value(MeasurementType.CURRENT) == 1.0
        assert measurement.get_value(MeasurementType.TEMPERATURE) == 25.0
        assert measurement.get_value(MeasurementType.CAPACITY) is None
    
    def test_measurement_with_derived_calculations(self):
        """Test adding derived calculations to measurement."""
        original = Measurement(
            id=uuid4(),
            timestamp=datetime.utcnow(),
            channel_id="ch1",
            voltage=3.7,
            current=1.0,
        )
        
        derived = original.with_derived_calculations(
            capacity=1.5,
            energy=5.55,
            power=3.7,
            soc=75.0,
        )
        
        assert derived.voltage == 3.7  # Original values preserved
        assert derived.current == 1.0
        assert derived.capacity == 1.5  # New derived values
        assert derived.energy == 5.55
        assert derived.power == 3.7
        assert derived.soc == 75.0
        assert derived.has_derived_data
        assert derived.has_state_data
    
    def test_measurement_serialization(self):
        """Test measurement to/from dict conversion."""
        original = Measurement(
            id=uuid4(),
            timestamp=datetime.utcnow(),
            channel_id="ch1",
            voltage=3.7,
            current=1.0,
            temperature=25.0,
            metadata={"test": "value"},
        )
        
        # Convert to dict
        data = original.to_dict()
        assert data["voltage"] == 3.7
        assert data["current"] == 1.0
        assert data["temperature"] == 25.0
        assert data["metadata"]["test"] == "value"
        
        # Convert back from dict
        restored = Measurement.from_dict(data)
        assert restored.id == original.id
        assert restored.voltage == original.voltage
        assert restored.current == original.current
        assert restored.temperature == original.temperature
        assert restored.metadata == original.metadata


class TestChannel:
    """Test cases for Channel entity."""
    
    def test_channel_creation(self, mock_instrument):
        """Test basic channel creation."""
        channel = Channel(
            id="ch1",
            name="Channel 1",
            instrument_id=mock_instrument.id,
            hardware_channel=1,
        )
        
        assert channel.id == "ch1"
        assert channel.name == "Channel 1"
        assert channel.instrument_id == mock_instrument.id
        assert channel.hardware_channel == 1
        assert channel.state == ChannelState.IDLE
        assert channel.is_available
    
    def test_channel_validation(self):
        """Test channel validation."""
        with pytest.raises(ValueError, match="Channel ID cannot be empty"):
            Channel(id="", name="Test", instrument_id="inst1")
        
        with pytest.raises(ValueError, match="Channel name cannot be empty"):
            Channel(id="ch1", name="", instrument_id="inst1")
        
        with pytest.raises(ValueError, match="Instrument ID cannot be empty"):
            Channel(id="ch1", name="Test", instrument_id="")
    
    def test_channel_test_lifecycle(self):
        """Test channel test lifecycle management."""
        channel = Channel(
            id="ch1",
            name="Channel 1",
            instrument_id="inst1",
        )
        
        test_id = uuid4()
        
        # Start test
        channel.start_test(test_id)
        assert channel.current_test_id == test_id
        assert channel.state == ChannelState.RUNNING
        assert not channel.is_available
        
        # Pause test
        channel.pause_test()
        assert channel.state == ChannelState.PAUSED
        assert channel.is_paused
        
        # Resume test
        channel.resume_test()
        assert channel.state == ChannelState.RUNNING
        assert channel.is_running
        
        # Complete test
        channel.complete_test()
        assert channel.current_test_id is None
        assert channel.state == ChannelState.IDLE
        assert channel.is_available
    
    def test_channel_error_handling(self):
        """Test channel error handling."""
        channel = Channel(
            id="ch1",
            name="Channel 1",
            instrument_id="inst1",
        )
        
        # Set error
        error_msg = "Test error"
        channel.set_error(error_msg)
        assert channel.has_error
        assert channel.error_message == error_msg
        assert channel.state == ChannelState.ERROR
        
        # Clear error
        channel.clear_error()
        assert not channel.has_error
        assert channel.error_message is None
        assert channel.state == ChannelState.IDLE
    
    def test_channel_safety_limits(self, safety_limits_strict):
        """Test channel safety limit checking."""
        channel = Channel(
            id="ch1",
            name="Channel 1",
            instrument_id="inst1",
            safety_limits=safety_limits_strict,
        )
        
        # Safe measurement
        safe_measurement = Measurement(
            id=uuid4(),
            timestamp=datetime.utcnow(),
            channel_id="ch1",
            voltage=3.7,
            current=1.0,
            temperature=25.0,
        )
        
        violations = channel.check_safety_limits(safe_measurement)
        assert len(violations) == 0
        
        # Unsafe measurement
        unsafe_measurement = Measurement(
            id=uuid4(),
            timestamp=datetime.utcnow(),
            channel_id="ch1",
            voltage=5.0,  # Exceeds max_voltage=4.5
            current=60.0,  # Exceeds max_current=50.0
            temperature=50.0,  # Exceeds max_temperature=45.0
        )
        
        violations = channel.check_safety_limits(unsafe_measurement)
        assert len(violations) == 3  # All three limits violated


class TestSafetyLimits:
    """Test cases for SafetyLimits value object."""
    
    def test_safety_limits_creation(self):
        """Test basic safety limits creation."""
        limits = SafetyLimits(
            max_voltage=4.2,
            min_voltage=2.5,
            max_current=10.0,
            max_temperature=60.0,
        )
        
        assert limits.max_voltage == 4.2
        assert limits.min_voltage == 2.5
        assert limits.max_current == 10.0
        assert limits.max_temperature == 60.0
    
    def test_safety_limits_validation(self):
        """Test safety limits validation."""
        # Test invalid voltage range
        with pytest.raises(ValueError, match="Maximum voltage must be greater than minimum voltage"):
            SafetyLimits(max_voltage=2.0, min_voltage=4.0)
        
        # Test invalid SOC range
        with pytest.raises(ValueError, match="Maximum SOC must be greater than minimum SOC"):
            SafetyLimits(max_soc=20.0, min_soc=80.0)
        
        # Test invalid SOC values
        with pytest.raises(ValueError, match="Maximum SOC must be between 0 and 100"):
            SafetyLimits(max_soc=150.0)
    
    def test_safety_limits_checking(self):
        """Test safety limit checking methods."""
        limits = SafetyLimits(
            max_voltage=4.2,
            min_voltage=2.5,
            max_current=10.0,
            max_temperature=60.0,
            max_soc=95.0,
            min_soc=5.0,
        )
        
        # Test voltage checking
        assert limits.check_voltage(3.7)
        assert not limits.check_voltage(5.0)
        assert not limits.check_voltage(2.0)
        
        # Test current checking
        assert limits.check_current(5.0)
        assert not limits.check_current(15.0)
        assert not limits.check_current(-15.0)
        
        # Test temperature checking
        assert limits.check_temperature(25.0)
        assert not limits.check_temperature(70.0)
        
        # Test SOC checking
        assert limits.check_soc(50.0)
        assert not limits.check_soc(100.0)
        assert not limits.check_soc(0.0)
    
    def test_safety_limits_violations(self):
        """Test getting violation messages."""
        limits = SafetyLimits(
            max_voltage=4.2,
            max_current=10.0,
            max_temperature=60.0,
        )
        
        violations = limits.get_violations(
            voltage=5.0,
            current=15.0,
            temperature=70.0,
        )
        
        assert len(violations) == 3
        assert any("voltage" in v.lower() for v in violations)
        assert any("current" in v.lower() for v in violations)
        assert any("temperature" in v.lower() for v in violations)
    
    def test_default_limits(self):
        """Test default safety limits."""
        default_limits = SafetyLimits.default_battery_limits()
        assert default_limits.max_voltage == 5.0
        assert default_limits.max_current == 100.0
        assert default_limits.max_temperature == 60.0
        
        strict_limits = SafetyLimits.strict_battery_limits()
        assert strict_limits.max_voltage == 4.5
        assert strict_limits.max_current == 50.0
        assert strict_limits.max_temperature == 45.0


class TestTestStep:
    """Test cases for TestStep entity."""
    
    def test_test_step_creation(self):
        """Test basic test step creation."""
        step = TestStep(
            step_number=1,
            name="CC Charge",
            step_type=StepType.CC,
            target_value=1.0,
            limit_value=4.2,
            limit_type="voltage",
            duration=3600,
        )
        
        assert step.step_number == 1
        assert step.name == "CC Charge"
        assert step.step_type == StepType.CC
        assert step.target_value == 1.0
        assert step.limit_value == 4.2
        assert step.limit_type == "voltage"
        assert step.duration == 3600
    
    def test_test_step_validation(self):
        """Test test step validation."""
        with pytest.raises(ValueError, match="Step number must be positive"):
            TestStep(step_number=0, name="Test", step_type=StepType.CC)
        
        with pytest.raises(ValueError, match="Step name cannot be empty"):
            TestStep(step_number=1, name="", step_type=StepType.CC)
        
        with pytest.raises(ValueError, match="Duration must be positive"):
            TestStep(step_number=1, name="Test", step_type=StepType.CC, duration=-1)
    
    def test_test_step_properties(self):
        """Test test step property methods."""
        # Charge step
        charge_step = TestStep(
            step_number=1,
            name="CC Charge",
            step_type=StepType.CC,
            target_value=1.0,
        )
        assert charge_step.is_charge_step
        assert not charge_step.is_discharge_step
        assert not charge_step.is_rest_step
        
        # Discharge step
        discharge_step = TestStep(
            step_number=2,
            name="CC Discharge",
            step_type=StepType.CC,
            target_value=-2.0,
        )
        assert not discharge_step.is_charge_step
        assert discharge_step.is_discharge_step
        assert not discharge_step.is_rest_step
        
        # Rest step
        rest_step = TestStep(
            step_number=3,
            name="Rest",
            step_type=StepType.REST,
            duration=300,
        )
        assert not rest_step.is_charge_step
        assert not rest_step.is_discharge_step
        assert rest_step.is_rest_step


class TestTest:
    """Test cases for Test entity."""
    
    def test_test_creation(self, sample_test_profile):
        """Test basic test creation."""
        test = Test(
            name="Test Battery",
            description="Test description",
            profile_id=sample_test_profile.id,
            steps=sample_test_profile.steps.copy(),
            total_cycles=3,
        )
        
        assert test.name == "Test Battery"
        assert test.description == "Test description"
        assert test.profile_id == sample_test_profile.id
        assert len(test.steps) == len(sample_test_profile.steps)
        assert test.total_cycles == 3
        assert test.state == TestState.CREATED
        assert test.can_start
    
    def test_test_lifecycle(self, sample_test):
        """Test test lifecycle management."""
        test = sample_test
        channel_id = "ch1"
        
        # Start test
        test.start(channel_id)
        assert test.state == TestState.RUNNING
        assert test.channel_id == channel_id
        assert test.is_running
        assert test.can_pause
        assert not test.can_start
        
        # Pause test
        test.pause()
        assert test.state == TestState.PAUSED
        assert test.is_paused
        assert test.can_resume
        
        # Resume test
        test.resume()
        assert test.state == TestState.RUNNING
        assert test.is_running
        
        # Complete test
        test.complete()
        assert test.state == TestState.COMPLETED
        assert test.is_completed
        assert test.completed_at is not None
    
    def test_test_progress_calculation(self, sample_test):
        """Test test progress calculation."""
        test = sample_test
        test.start("ch1")
        
        # At start of first cycle, first step
        assert test.progress_percentage == 0.0
        
        # Advance to step 2 of cycle 1
        test.advance_step()
        progress = test.progress_percentage
        expected = (1 / (3 * 3)) * 100  # 1 step out of 9 total (3 steps × 3 cycles)
        assert abs(progress - expected) < 0.1
        
        # Complete test
        test.complete()
        assert test.progress_percentage == 100.0
