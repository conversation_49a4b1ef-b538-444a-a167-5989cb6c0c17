"""User entity for authentication and authorization."""

from dataclasses import dataclass, field
from datetime import datetime
from enum import Enum
from typing import Dict, Any, Optional, List
from uuid import UUID, uuid4


class UserRole(Enum):
    """User roles with different permission levels."""
    ADMIN = "admin"        # Full system access
    OPERATOR = "operator"  # Can run tests and view data
    VIEWER = "viewer"      # Read-only access


@dataclass
class User:
    """
    Represents a system user with authentication and authorization.
    """
    
    # Core identification
    id: UUID = field(default_factory=uuid4)
    username: str = ""
    email: Optional[str] = None
    full_name: Optional[str] = None
    
    # Authentication
    password_hash: Optional[str] = None
    is_active: bool = True
    is_verified: bool = False
    
    # Authorization
    role: UserRole = UserRole.VIEWER
    permissions: List[str] = field(default_factory=list)
    
    # Timestamps
    created_at: datetime = field(default_factory=datetime.utcnow)
    updated_at: datetime = field(default_factory=datetime.utcnow)
    last_login: Optional[datetime] = None
    
    # Security
    failed_login_attempts: int = 0
    locked_until: Optional[datetime] = None
    
    # Preferences
    preferences: Dict[str, Any] = field(default_factory=dict)
    
    # Metadata
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    def __post_init__(self):
        """Validate user data after initialization."""
        if not self.username:
            raise ValueError("Username cannot be empty")
        
        if self.email and "@" not in self.email:
            raise ValueError("Invalid email format")
    
    @property
    def is_admin(self) -> bool:
        """Check if user has admin role."""
        return self.role == UserRole.ADMIN
    
    @property
    def is_operator(self) -> bool:
        """Check if user has operator role or higher."""
        return self.role in [UserRole.ADMIN, UserRole.OPERATOR]
    
    @property
    def is_locked(self) -> bool:
        """Check if user account is locked."""
        if self.locked_until is None:
            return False
        return datetime.utcnow() < self.locked_until
    
    @property
    def can_login(self) -> bool:
        """Check if user can log in."""
        return self.is_active and not self.is_locked
    
    def has_permission(self, permission: str) -> bool:
        """Check if user has a specific permission."""
        if self.is_admin:
            return True  # Admins have all permissions
        return permission in self.permissions
    
    def add_permission(self, permission: str) -> None:
        """Add a permission to the user."""
        if permission not in self.permissions:
            self.permissions.append(permission)
            self._update_timestamp()
    
    def remove_permission(self, permission: str) -> None:
        """Remove a permission from the user."""
        if permission in self.permissions:
            self.permissions.remove(permission)
            self._update_timestamp()
    
    def record_login(self) -> None:
        """Record a successful login."""
        self.last_login = datetime.utcnow()
        self.failed_login_attempts = 0
        self.locked_until = None
        self._update_timestamp()
    
    def record_failed_login(self, max_attempts: int = 5, lockout_minutes: int = 15) -> None:
        """Record a failed login attempt."""
        self.failed_login_attempts += 1
        
        if self.failed_login_attempts >= max_attempts:
            from datetime import timedelta
            self.locked_until = datetime.utcnow() + timedelta(minutes=lockout_minutes)
        
        self._update_timestamp()
    
    def unlock_account(self) -> None:
        """Unlock the user account."""
        self.failed_login_attempts = 0
        self.locked_until = None
        self._update_timestamp()
    
    def set_preference(self, key: str, value: Any) -> None:
        """Set a user preference."""
        self.preferences[key] = value
        self._update_timestamp()
    
    def get_preference(self, key: str, default: Any = None) -> Any:
        """Get a user preference."""
        return self.preferences.get(key, default)
    
    def to_dict(self, include_sensitive: bool = False) -> Dict[str, Any]:
        """Convert user to dictionary for serialization."""
        data = {
            "id": str(self.id),
            "username": self.username,
            "email": self.email,
            "full_name": self.full_name,
            "is_active": self.is_active,
            "is_verified": self.is_verified,
            "role": self.role.value,
            "permissions": self.permissions,
            "created_at": self.created_at.isoformat(),
            "updated_at": self.updated_at.isoformat(),
            "last_login": self.last_login.isoformat() if self.last_login else None,
            "preferences": self.preferences,
            "metadata": self.metadata,
        }
        
        if include_sensitive:
            data.update({
                "password_hash": self.password_hash,
                "failed_login_attempts": self.failed_login_attempts,
                "locked_until": self.locked_until.isoformat() if self.locked_until else None,
            })
        
        return data
    
    def _update_timestamp(self) -> None:
        """Update the last modified timestamp."""
        self.updated_at = datetime.utcnow()
