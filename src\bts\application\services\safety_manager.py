"""Safety management service for battery testing."""

import asyncio
import logging
from datetime import datetime
from typing import Dict, List, Optional, Callable, Any
from enum import Enum

from ...domain.entities.measurement import Measurement
from ...domain.entities.channel import Channel
from ...domain.value_objects.safety_limits import SafetyLimits


class SafetyLevel(Enum):
    """Safety alert levels."""
    INFO = "info"
    WARNING = "warning"
    CRITICAL = "critical"
    EMERGENCY = "emergency"


class SafetyEvent:
    """Represents a safety event."""
    
    def __init__(
        self,
        level: SafetyLevel,
        message: str,
        channel_id: Optional[str] = None,
        measurement: Optional[Measurement] = None,
        timestamp: Optional[datetime] = None,
    ):
        self.level = level
        self.message = message
        self.channel_id = channel_id
        self.measurement = measurement
        self.timestamp = timestamp or datetime.utcnow()


class SafetyManager:
    """
    Service for managing safety monitoring and emergency responses.
    
    Monitors measurements in real-time, enforces safety limits,
    and triggers emergency stops when necessary.
    """
    
    def __init__(self):
        """Initialize the safety manager."""
        self.logger = logging.getLogger(__name__)
        
        # Safety state
        self._global_safety_limits: Optional[SafetyLimits] = None
        self._channel_safety_limits: Dict[str, SafetyLimits] = {}
        self._safety_enabled = True
        self._emergency_stop_active = False
        
        # Event tracking
        self._safety_events: List[SafetyEvent] = []
        self._max_events = 1000
        
        # Callbacks
        self._safety_event_callbacks: List[Callable[[SafetyEvent], None]] = []
        self._emergency_stop_callbacks: List[Callable[[], None]] = []
        
        # Monitoring
        self._monitoring_active = False
        self._monitoring_task: Optional[asyncio.Task] = None
    
    @property
    def is_emergency_stop_active(self) -> bool:
        """Check if emergency stop is active."""
        return self._emergency_stop_active
    
    @property
    def safety_enabled(self) -> bool:
        """Check if safety monitoring is enabled."""
        return self._safety_enabled
    
    async def start(self) -> None:
        """Start safety monitoring."""
        if self._monitoring_active:
            return
        
        self.logger.info("Starting safety manager...")
        self._monitoring_active = True
        
        # Load default safety limits
        self._global_safety_limits = SafetyLimits.default_battery_limits()
        
        self.logger.info("Safety manager started")
    
    async def stop(self) -> None:
        """Stop safety monitoring."""
        if not self._monitoring_active:
            return
        
        self.logger.info("Stopping safety manager...")
        self._monitoring_active = False
        
        if self._monitoring_task:
            self._monitoring_task.cancel()
            try:
                await self._monitoring_task
            except asyncio.CancelledError:
                pass
        
        self.logger.info("Safety manager stopped")
    
    def set_global_safety_limits(self, limits: SafetyLimits) -> None:
        """Set global safety limits."""
        self._global_safety_limits = limits
        self.logger.info("Updated global safety limits")
    
    def set_channel_safety_limits(self, channel_id: str, limits: SafetyLimits) -> None:
        """Set safety limits for a specific channel."""
        self._channel_safety_limits[channel_id] = limits
        self.logger.info(f"Updated safety limits for channel {channel_id}")
    
    def enable_safety(self) -> None:
        """Enable safety monitoring."""
        self._safety_enabled = True
        self.logger.info("Safety monitoring enabled")
    
    def disable_safety(self) -> None:
        """Disable safety monitoring (use with caution!)."""
        self._safety_enabled = False
        self.logger.warning("Safety monitoring disabled")
    
    async def check_measurement_safety(
        self,
        measurement: Measurement,
        channel: Optional[Channel] = None,
    ) -> List[SafetyEvent]:
        """
        Check a measurement against safety limits.
        
        Args:
            measurement: Measurement to check
            channel: Channel information (optional)
            
        Returns:
            List of safety events if violations found
        """
        if not self._safety_enabled:
            return []
        
        events = []
        
        # Get applicable safety limits
        limits = self._get_applicable_limits(measurement.channel_id)
        if not limits:
            return []
        
        # Check voltage limits
        if measurement.voltage is not None:
            if not limits.check_voltage(measurement.voltage):
                event = SafetyEvent(
                    level=SafetyLevel.CRITICAL,
                    message=f"Voltage {measurement.voltage}V violates safety limits",
                    channel_id=measurement.channel_id,
                    measurement=measurement,
                )
                events.append(event)
        
        # Check current limits
        if measurement.current is not None:
            if not limits.check_current(measurement.current):
                event = SafetyEvent(
                    level=SafetyLevel.CRITICAL,
                    message=f"Current {measurement.current}A violates safety limits",
                    channel_id=measurement.channel_id,
                    measurement=measurement,
                )
                events.append(event)
        
        # Check temperature limits
        if measurement.temperature is not None:
            if not limits.check_temperature(measurement.temperature):
                event = SafetyEvent(
                    level=SafetyLevel.CRITICAL,
                    message=f"Temperature {measurement.temperature}°C violates safety limits",
                    channel_id=measurement.channel_id,
                    measurement=measurement,
                )
                events.append(event)
        
        # Check SOC limits
        if measurement.soc is not None:
            if not limits.check_soc(measurement.soc):
                event = SafetyEvent(
                    level=SafetyLevel.WARNING,
                    message=f"SOC {measurement.soc}% violates safety limits",
                    channel_id=measurement.channel_id,
                    measurement=measurement,
                )
                events.append(event)
        
        # Process events
        for event in events:
            await self._process_safety_event(event)
        
        return events
    
    async def trigger_emergency_stop(self, reason: str) -> None:
        """Trigger emergency stop of all operations."""
        if self._emergency_stop_active:
            return
        
        self.logger.critical(f"EMERGENCY STOP TRIGGERED: {reason}")
        self._emergency_stop_active = True
        
        # Create emergency event
        event = SafetyEvent(
            level=SafetyLevel.EMERGENCY,
            message=f"Emergency stop: {reason}",
        )
        await self._process_safety_event(event)
        
        # Notify callbacks
        for callback in self._emergency_stop_callbacks:
            try:
                callback()
            except Exception as e:
                self.logger.error(f"Error in emergency stop callback: {e}")
    
    async def clear_emergency_stop(self) -> None:
        """Clear emergency stop condition."""
        if not self._emergency_stop_active:
            return
        
        self.logger.info("Clearing emergency stop")
        self._emergency_stop_active = False
        
        event = SafetyEvent(
            level=SafetyLevel.INFO,
            message="Emergency stop cleared",
        )
        await self._process_safety_event(event)
    
    def get_recent_events(self, count: int = 100) -> List[SafetyEvent]:
        """Get recent safety events."""
        return self._safety_events[-count:]
    
    def get_events_by_level(self, level: SafetyLevel) -> List[SafetyEvent]:
        """Get events by safety level."""
        return [event for event in self._safety_events if event.level == level]
    
    def add_safety_event_callback(self, callback: Callable[[SafetyEvent], None]) -> None:
        """Add callback for safety events."""
        self._safety_event_callbacks.append(callback)
    
    def add_emergency_stop_callback(self, callback: Callable[[], None]) -> None:
        """Add callback for emergency stops."""
        self._emergency_stop_callbacks.append(callback)
    
    def _get_applicable_limits(self, channel_id: str) -> Optional[SafetyLimits]:
        """Get applicable safety limits for a channel."""
        # Channel-specific limits take precedence
        if channel_id in self._channel_safety_limits:
            return self._channel_safety_limits[channel_id]
        
        # Fall back to global limits
        return self._global_safety_limits
    
    async def _process_safety_event(self, event: SafetyEvent) -> None:
        """Process a safety event."""
        # Store event
        self._safety_events.append(event)
        
        # Trim event list if too long
        if len(self._safety_events) > self._max_events:
            self._safety_events = self._safety_events[-self._max_events:]
        
        # Log event
        log_level = {
            SafetyLevel.INFO: logging.INFO,
            SafetyLevel.WARNING: logging.WARNING,
            SafetyLevel.CRITICAL: logging.ERROR,
            SafetyLevel.EMERGENCY: logging.CRITICAL,
        }.get(event.level, logging.INFO)
        
        self.logger.log(log_level, f"Safety event: {event.message}")
        
        # Trigger emergency stop for critical events
        if event.level == SafetyLevel.EMERGENCY and not self._emergency_stop_active:
            await self.trigger_emergency_stop(event.message)
        
        # Notify callbacks
        for callback in self._safety_event_callbacks:
            try:
                callback(event)
            except Exception as e:
                self.logger.error(f"Error in safety event callback: {e}")
    
    async def check_external_safety_systems(self) -> List[SafetyEvent]:
        """Check external safety systems (fire alarms, etc.)."""
        events = []
        
        # Implementation would check:
        # - Fire alarm systems
        # - Temperature chambers
        # - Pressure sensors
        # - Gas detection systems
        # - UPS status
        # - Network connectivity
        
        return events
