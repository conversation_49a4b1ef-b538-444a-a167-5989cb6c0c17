"""GUI widgets for the Battery Testing System."""

# Widget imports will be added as widgets are implemented
try:
    from .dashboard_widget import DashboardWidget
    from .instruments_widget import InstrumentsWidget
    from .tests_widget import TestsWidget
    from .data_widget import DataWidget
    from .settings_widget import SettingsWidget
    
    __all__ = [
        "DashboardWidget",
        "InstrumentsWidget", 
        "TestsWidget",
        "DataWidget",
        "SettingsWidget",
    ]
except ImportError:
    # Widgets not yet implemented
    __all__ = []
