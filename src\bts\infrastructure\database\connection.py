"""Database connection management."""

import asyncio
import logging
from typing import Op<PERSON>, AsyncGenerator
from contextlib import asynccontextmanager

try:
    from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession, async_sessionmaker
    from sqlalchemy.pool import StaticPool
    SQLALCHEMY_AVAILABLE = True
except ImportError:
    SQLALCHEMY_AVAILABLE = False

from ..config.settings import Settings


class DatabaseConnection:
    """
    Manages database connections for the Battery Testing System.
    
    Supports both SQLite and PostgreSQL backends with connection pooling
    and automatic reconnection.
    """
    
    def __init__(self, settings: Settings):
        """Initialize database connection manager."""
        self.settings = settings
        self.logger = logging.getLogger(__name__)
        
        if not SQLALCHEMY_AVAILABLE:
            raise ImportError("SQLAlchemy is required for database operations")
        
        # Database engines
        self._primary_engine = None
        self._timeseries_engine = None
        
        # Session makers
        self._primary_session_maker = None
        self._timeseries_session_maker = None
        
        # Connection state
        self._connected = False
    
    async def connect(self) -> bool:
        """Connect to databases."""
        try:
            self.logger.info("Connecting to databases...")
            
            # Create primary database engine
            primary_url = self._convert_url_for_async(self.settings.database.primary)
            self._primary_engine = create_async_engine(
                primary_url,
                pool_size=self.settings.database.max_connections,
                max_overflow=10,
                pool_timeout=self.settings.database.connection_timeout,
                pool_recycle=3600,
                echo=self.settings.debug_mode,
                poolclass=StaticPool if "sqlite" in primary_url else None,
                connect_args={"check_same_thread": False} if "sqlite" in primary_url else {},
            )
            
            # Create timeseries database engine
            timeseries_url = self._convert_url_for_async(self.settings.database.timeseries)
            self._timeseries_engine = create_async_engine(
                timeseries_url,
                pool_size=self.settings.database.max_connections,
                max_overflow=10,
                pool_timeout=self.settings.database.connection_timeout,
                pool_recycle=3600,
                echo=self.settings.debug_mode,
                poolclass=StaticPool if "sqlite" in timeseries_url else None,
                connect_args={"check_same_thread": False} if "sqlite" in timeseries_url else {},
            )
            
            # Create session makers
            self._primary_session_maker = async_sessionmaker(
                self._primary_engine,
                class_=AsyncSession,
                expire_on_commit=False,
            )
            
            self._timeseries_session_maker = async_sessionmaker(
                self._timeseries_engine,
                class_=AsyncSession,
                expire_on_commit=False,
            )
            
            # Test connections
            await self._test_connections()
            
            self._connected = True
            self.logger.info("Database connections established")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to connect to databases: {e}")
            await self.disconnect()
            return False
    
    async def disconnect(self) -> None:
        """Disconnect from databases."""
        try:
            self.logger.info("Disconnecting from databases...")
            
            if self._primary_engine:
                await self._primary_engine.dispose()
                self._primary_engine = None
            
            if self._timeseries_engine:
                await self._timeseries_engine.dispose()
                self._timeseries_engine = None
            
            self._primary_session_maker = None
            self._timeseries_session_maker = None
            self._connected = False
            
            self.logger.info("Database connections closed")
            
        except Exception as e:
            self.logger.error(f"Error disconnecting from databases: {e}")
    
    @property
    def is_connected(self) -> bool:
        """Check if connected to databases."""
        return self._connected
    
    @asynccontextmanager
    async def get_primary_session(self) -> AsyncGenerator[AsyncSession, None]:
        """Get a session for the primary database."""
        if not self._primary_session_maker:
            raise RuntimeError("Database not connected")
        
        async with self._primary_session_maker() as session:
            try:
                yield session
                await session.commit()
            except Exception:
                await session.rollback()
                raise
    
    @asynccontextmanager
    async def get_timeseries_session(self) -> AsyncGenerator[AsyncSession, None]:
        """Get a session for the timeseries database."""
        if not self._timeseries_session_maker:
            raise RuntimeError("Database not connected")
        
        async with self._timeseries_session_maker() as session:
            try:
                yield session
                await session.commit()
            except Exception:
                await session.rollback()
                raise
    
    async def create_tables(self) -> bool:
        """Create database tables."""
        try:
            from .models import Base
            
            # Create tables in primary database
            if self._primary_engine:
                async with self._primary_engine.begin() as conn:
                    await conn.run_sync(Base.metadata.create_all)
            
            # Create tables in timeseries database
            if self._timeseries_engine:
                async with self._timeseries_engine.begin() as conn:
                    await conn.run_sync(Base.metadata.create_all)
            
            self.logger.info("Database tables created")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to create database tables: {e}")
            return False
    
    async def drop_tables(self) -> bool:
        """Drop database tables."""
        try:
            from .models import Base
            
            # Drop tables in primary database
            if self._primary_engine:
                async with self._primary_engine.begin() as conn:
                    await conn.run_sync(Base.metadata.drop_all)
            
            # Drop tables in timeseries database
            if self._timeseries_engine:
                async with self._timeseries_engine.begin() as conn:
                    await conn.run_sync(Base.metadata.drop_all)
            
            self.logger.info("Database tables dropped")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to drop database tables: {e}")
            return False
    
    async def get_connection_info(self) -> dict:
        """Get database connection information."""
        return {
            "connected": self.is_connected,
            "primary_url": self._mask_password(self.settings.database.primary),
            "timeseries_url": self._mask_password(self.settings.database.timeseries),
            "pool_size": self.settings.database.max_connections,
            "connection_timeout": self.settings.database.connection_timeout,
        }
    
    def _convert_url_for_async(self, url: str) -> str:
        """Convert database URL for async usage."""
        if url.startswith("sqlite:///"):
            return url.replace("sqlite:///", "sqlite+aiosqlite:///")
        elif url.startswith("postgresql://"):
            return url.replace("postgresql://", "postgresql+asyncpg://")
        elif url.startswith("mysql://"):
            return url.replace("mysql://", "mysql+aiomysql://")
        else:
            return url
    
    def _mask_password(self, url: str) -> str:
        """Mask password in database URL for logging."""
        if "://" in url and "@" in url:
            parts = url.split("://")
            if len(parts) == 2:
                scheme = parts[0]
                rest = parts[1]
                if "@" in rest:
                    auth_part, host_part = rest.split("@", 1)
                    if ":" in auth_part:
                        user, _ = auth_part.split(":", 1)
                        return f"{scheme}://{user}:***@{host_part}"
        return url
    
    async def _test_connections(self) -> None:
        """Test database connections."""
        # Test primary database
        if self._primary_engine:
            async with self._primary_engine.begin() as conn:
                await conn.execute("SELECT 1" if "postgresql" in str(self._primary_engine.url) else "SELECT 1")
        
        # Test timeseries database
        if self._timeseries_engine:
            async with self._timeseries_engine.begin() as conn:
                await conn.execute("SELECT 1" if "postgresql" in str(self._timeseries_engine.url) else "SELECT 1")
    
    async def __aenter__(self):
        """Async context manager entry."""
        await self.connect()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit."""
        await self.disconnect()
