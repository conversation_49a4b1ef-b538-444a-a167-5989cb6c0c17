"""Main GUI application for Battery Testing System."""

import sys
import asyncio
import logging
from pathlib import Path
from typing import Optional

try:
    from PySide6.QtWidgets import QApplication, QMessageBox
    from PySide6.QtCore import QTimer, Qt
    from PySide6.QtGui import QIcon
    PYSIDE6_AVAILABLE = True
except ImportError:
    PYSIDE6_AVAILABLE = False
    QApplication = None

from ..infrastructure.config.settings import Settings
from ..infrastructure.database.connection import DatabaseConnection
from ..application.services.hardware_manager import HardwareManager
from ..application.services.data_manager import DataManager
from ..application.services.safety_manager import SafetyManager
from ..application.services.test_engine import TestEngine


class BtsApplication:
    """Main Battery Testing System GUI application."""
    
    def __init__(self):
        """Initialize the BTS application."""
        if not PYSIDE6_AVAILABLE:
            raise ImportError(
                "PySide6 is required for GUI functionality. "
                "Install with: pip install PySide6"
            )
        
        # Setup logging
        self.logger = logging.getLogger(__name__)
        
        # Qt Application
        self.qt_app: Optional[QApplication] = None
        self.main_window = None
        
        # BTS Services
        self.settings: Optional[Settings] = None
        self.database: Optional[DatabaseConnection] = None
        self.hardware_manager: Optional[HardwareManager] = None
        self.data_manager: Optional[DataManager] = None
        self.safety_manager: Optional[SafetyManager] = None
        self.test_engine: Optional[TestEngine] = None
        
        # Application state
        self.is_initialized = False
        self.is_running = False
        
        # Async event loop integration
        self.event_loop = None
        self.async_timer = None
    
    def initialize(self, config_path: Optional[str] = None) -> bool:
        """Initialize the application and services."""
        try:
            self.logger.info("Initializing BTS GUI application...")
            
            # Load settings
            self.settings = Settings.load_from_file(config_path)
            
            # Setup logging from settings
            log_config = self.settings.get_log_config()
            logging.config.dictConfig(log_config)
            
            # Create Qt application
            self.qt_app = QApplication(sys.argv)
            self.qt_app.setApplicationName("Battery Testing System")
            self.qt_app.setApplicationVersion("0.1.0")
            self.qt_app.setOrganizationName("BTS Team")
            
            # Set application icon
            icon_path = Path(__file__).parent / "resources" / "bts_icon.png"
            if icon_path.exists():
                self.qt_app.setWindowIcon(QIcon(str(icon_path)))
            
            # Apply theme
            self._apply_theme()
            
            # Initialize async event loop
            self._setup_async_integration()
            
            self.is_initialized = True
            self.logger.info("BTS GUI application initialized successfully")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to initialize BTS application: {e}")
            if self.qt_app:
                QMessageBox.critical(
                    None,
                    "Initialization Error",
                    f"Failed to initialize BTS application:\n{e}"
                )
            return False
    
    async def start_services(self) -> bool:
        """Start all BTS services."""
        try:
            self.logger.info("Starting BTS services...")
            
            # Initialize database
            self.database = DatabaseConnection(self.settings)
            if not await self.database.connect():
                raise Exception("Failed to connect to database")
            
            # Create tables if they don't exist
            await self.database.create_tables()
            
            # Initialize services
            self.hardware_manager = HardwareManager()
            self.data_manager = DataManager(self.settings.database.dict())
            self.safety_manager = SafetyManager()
            self.test_engine = TestEngine(
                self.hardware_manager,
                self.data_manager,
                self.safety_manager
            )
            
            # Start services
            await self.hardware_manager.start()
            await self.data_manager.start()
            await self.safety_manager.start()
            await self.test_engine.start()
            
            self.logger.info("All BTS services started successfully")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to start BTS services: {e}")
            await self.stop_services()
            return False
    
    async def stop_services(self) -> None:
        """Stop all BTS services."""
        try:
            self.logger.info("Stopping BTS services...")
            
            if self.test_engine:
                await self.test_engine.stop()
            
            if self.safety_manager:
                await self.safety_manager.stop()
            
            if self.data_manager:
                await self.data_manager.stop()
            
            if self.hardware_manager:
                await self.hardware_manager.stop()
            
            if self.database:
                await self.database.disconnect()
            
            self.logger.info("All BTS services stopped")
            
        except Exception as e:
            self.logger.error(f"Error stopping BTS services: {e}")
    
    def create_main_window(self):
        """Create and show the main window."""
        try:
            from .main_window import MainWindow
            
            self.main_window = MainWindow(
                settings=self.settings,
                hardware_manager=self.hardware_manager,
                data_manager=self.data_manager,
                safety_manager=self.safety_manager,
                test_engine=self.test_engine
            )
            
            self.main_window.show()
            self.logger.info("Main window created and shown")
            
        except Exception as e:
            self.logger.error(f"Failed to create main window: {e}")
            QMessageBox.critical(
                None,
                "Window Creation Error",
                f"Failed to create main window:\n{e}"
            )
    
    def run(self) -> int:
        """Run the GUI application."""
        if not self.is_initialized:
            self.logger.error("Application not initialized")
            return 1
        
        try:
            self.logger.info("Starting BTS GUI application...")
            self.is_running = True
            
            # Start services asynchronously
            asyncio.create_task(self._startup_sequence())
            
            # Run Qt event loop
            exit_code = self.qt_app.exec()
            
            self.logger.info(f"BTS GUI application exited with code {exit_code}")
            return exit_code
            
        except Exception as e:
            self.logger.error(f"Error running BTS application: {e}")
            return 1
        finally:
            self.is_running = False
            # Cleanup will be handled by async shutdown
    
    async def _startup_sequence(self):
        """Async startup sequence."""
        try:
            # Start services
            if await self.start_services():
                # Create main window
                self.create_main_window()
            else:
                # Show error and exit
                QMessageBox.critical(
                    None,
                    "Startup Error",
                    "Failed to start BTS services. Please check the logs."
                )
                self.qt_app.quit()
                
        except Exception as e:
            self.logger.error(f"Error in startup sequence: {e}")
            self.qt_app.quit()
    
    def _setup_async_integration(self):
        """Setup async/await integration with Qt event loop."""
        # Create event loop for async operations
        self.event_loop = asyncio.new_event_loop()
        asyncio.set_event_loop(self.event_loop)
        
        # Timer to process async events
        self.async_timer = QTimer()
        self.async_timer.timeout.connect(self._process_async_events)
        self.async_timer.start(10)  # Process every 10ms
    
    def _process_async_events(self):
        """Process async events in Qt event loop."""
        try:
            # Process pending async tasks
            if self.event_loop and self.event_loop.is_running():
                # Run one iteration of the event loop
                self.event_loop._run_once()
        except Exception as e:
            self.logger.error(f"Error processing async events: {e}")
    
    def _apply_theme(self):
        """Apply the selected theme to the application."""
        try:
            theme = self.settings.gui.theme if self.settings else "dark"
            
            if theme == "dark":
                try:
                    import qdarkstyle
                    self.qt_app.setStyleSheet(qdarkstyle.load_stylesheet_pyside6())
                except ImportError:
                    # Fallback to basic dark theme
                    self._apply_basic_dark_theme()
            elif theme == "light":
                # Use default Qt theme
                self.qt_app.setStyleSheet("")
            
            self.logger.info(f"Applied {theme} theme")
            
        except Exception as e:
            self.logger.warning(f"Failed to apply theme: {e}")
    
    def _apply_basic_dark_theme(self):
        """Apply a basic dark theme without qdarkstyle."""
        dark_stylesheet = """
        QMainWindow {
            background-color: #2b2b2b;
            color: #ffffff;
        }
        QWidget {
            background-color: #2b2b2b;
            color: #ffffff;
        }
        QPushButton {
            background-color: #404040;
            border: 1px solid #555555;
            padding: 5px;
            border-radius: 3px;
        }
        QPushButton:hover {
            background-color: #505050;
        }
        QPushButton:pressed {
            background-color: #353535;
        }
        QLineEdit, QTextEdit, QPlainTextEdit {
            background-color: #404040;
            border: 1px solid #555555;
            padding: 3px;
            border-radius: 3px;
        }
        QTabWidget::pane {
            border: 1px solid #555555;
        }
        QTabBar::tab {
            background-color: #404040;
            padding: 5px 10px;
            margin-right: 2px;
        }
        QTabBar::tab:selected {
            background-color: #505050;
        }
        """
        self.qt_app.setStyleSheet(dark_stylesheet)
    
    def shutdown(self):
        """Shutdown the application gracefully."""
        async def _shutdown():
            await self.stop_services()
        
        if self.event_loop:
            asyncio.run_coroutine_threadsafe(_shutdown(), self.event_loop)
        
        if self.qt_app:
            self.qt_app.quit()


def main():
    """Main entry point for the GUI application."""
    app = BtsApplication()
    
    if not app.initialize():
        return 1
    
    try:
        return app.run()
    except KeyboardInterrupt:
        print("\nShutting down...")
        return 0
    finally:
        app.shutdown()


if __name__ == "__main__":
    sys.exit(main())
